2025-08-26 16:08:44.690847: currentXmlPath:C:\Users\<USER>\Desktop\Release\Release\packages\sea_socket\lib\src\protocols
2025-08-26 16:08:44.691845: 61
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: planConfig
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: SysConfig
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: Sip2Config
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: pageConfig
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: readerConfig
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: banZhengConfig
2025-08-26 16:08:44.695834: ✅ 数据库更新成功: printConfig
2025-08-26 16:08:44.911119: 开始初始化闸机协调器...
2025-08-26 16:08:44.911119: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-26 16:08:44.912115: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-26 16:08:44.932048: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-26 16:08:44.932048: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-26 16:08:44.932048: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-26 16:08:44.936035: 连接闸机串口: COM1
2025-08-26 16:08:44.936035: 尝试连接串口: COM1, 波特率: 115200
2025-08-26 16:08:44.936035: 串口连接成功: COM1 at 115200 baud
2025-08-26 16:08:44.936035: 开始监听串口数据
2025-08-26 16:08:44.936035: 串口连接状态变化: true
2025-08-26 16:08:44.936035: 闸机串口连接成功
2025-08-26 16:08:44.937031: 串口 COM1 连接成功 (波特率: 115200)
2025-08-26 16:08:44.937031: 闸机串口服务初始化成功: COM1
2025-08-26 16:08:44.937031: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-26 16:08:44.937031: 开始监听闸机串口命令
2025-08-26 16:08:44.937031: 开始初始化RFID服务和共享池...
2025-08-26 16:08:44.937031: 开始初始化增强RFID服务...
2025-08-26 16:08:44.937031: 开始初始化增强RFID服务...
2025-08-26 16:08:44.938028: SIP2图书信息服务初始化完成
2025-08-26 16:08:44.938028: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-26 16:08:44.938028: 📋 从数据库读取主从机配置: channel_1
2025-08-26 16:08:44.938028: 📋 配置详情: 主机模式
2025-08-26 16:08:44.939024: 🚀 主机模式：启动RFID硬件持续扫描
2025-08-26 16:08:44.939024: 启动RFID持续扫描...
2025-08-26 16:08:44.939024: changeReaders
2025-08-26 16:08:44.939024: createIsolate isOpen:false,isOpening:false
2025-08-26 16:08:44.940021: createIsolate newport null
2025-08-26 16:08:44.961948: socket 连接成功,isBroadcast:false
2025-08-26 16:08:44.961948: changeSocketStatus:true
2025-08-26 16:08:44.962946: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-26 16:08:44.962946: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-26 16:08:45.135374: Rsp : 941AY1AZFDFC
2025-08-26 16:08:45.151323: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-26 16:08:45.152319: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-26 16:08:45.152319: 发送心跳
2025-08-26 16:08:45.153315: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-26 16:08:45.221090: Rsp : 98YYYNNN00500320250826    1608382.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD517
2025-08-26 16:08:45.441898: 找到网口配置: LSGate图书馆安全门RFID阅读器 - **************:6012
2025-08-26 16:08:45.442896: 使用网口连接: **************:6012
2025-08-26 16:08:45.443892: open():SendPort
2025-08-26 16:08:45.443892: untilDetcted():SendPort
2025-08-26 16:08:45.443892: 网口连接成功: **************:6012
2025-08-26 16:08:45.444888: startInventory():SendPort
2025-08-26 16:08:45.444888: RFID硬件扫描已启动，阅读器开始持续工作
2025-08-26 16:08:45.445886: RFID持续扫描启动完成
2025-08-26 16:08:45.445886: 增强RFID服务初始化完成，持续扫描已启动
2025-08-26 16:08:45.447881: 📋 从数据库读取主从机配置: channel_1
2025-08-26 16:08:45.448878: 📋 配置详情: 主机模式
2025-08-26 16:08:45.448878: 🚀 主机模式：启动持续数据收集，数据将持续进入共享池
2025-08-26 16:08:45.449872: 🎯 关键修复：使用轮询机制确保标签持续被发现
2025-08-26 16:08:45.449872: 🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...
2025-08-26 16:08:45.449872: 清空RFID扫描缓冲区...
2025-08-26 16:08:45.449872: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:08:45.450868: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:08:45.450868: 🔄 开始重置已处理条码集合...
2025-08-26 16:08:45.450868: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:08:45.450868: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:08:45.450868: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:08:45.451864: 📊 当前tagList状态: 0个标签
2025-08-26 16:08:45.451864: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 16:08:45.451864: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:08:45.451864: 🚀 启动增强数据收集（事件监听 + 轮询备用）...
2025-08-26 16:08:45.451864: 🚀 开始RFID数据收集...
2025-08-26 16:08:45.452861: 📋 扫描结果和缓存已清空
2025-08-26 16:08:45.452861: 清空RFID扫描缓冲区...
2025-08-26 16:08:45.452861: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:08:45.452861: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:08:45.452861: 🔄 开始重置已处理条码集合...
2025-08-26 16:08:45.452861: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:08:45.452861: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:08:45.452861: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:08:45.453857: 📊 当前tagList状态: 0个标签
2025-08-26 16:08:45.453857: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 16:08:45.453857: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:08:45.453857: 🎯 启动数据监听和轮询机制...
2025-08-26 16:08:45.453857: 🚀 标签轮询机制已启动 (每500ms轮询一次)
2025-08-26 16:08:45.453857: ✅ 标签监听改为仅轮询机制（每500ms轮询tagList）
2025-08-26 16:08:45.454860: 📊 当前HWTagProvider状态:
2025-08-26 16:08:45.454860:   - tagList: 0个标签
2025-08-26 16:08:45.454860:   - type: HWTagType.tagList
2025-08-26 16:08:45.454860: 🎯 标签数据获取已统一为轮询机制
2025-08-26 16:08:45.454860: ✅ RFID数据收集已启动，轮询机制运行中
2025-08-26 16:08:45.454860: 📊 当前tagList状态: 0个标签
2025-08-26 16:08:45.454860: subThread :ReaderCommand.readerList
2025-08-26 16:08:45.455851: commandRsp:ReaderCommand.readerList
2025-08-26 16:08:45.455851: readerList：1,readerSetting：1
2025-08-26 16:08:45.455851: cacheUsedReaders:1
2025-08-26 16:08:45.455851: subThread :ReaderCommand.open
2025-08-26 16:08:45.455851: commandRsp:ReaderCommand.open
2025-08-26 16:08:45.455851: LSGate使用网络连接 IP: **************, Port: 6012, DeviceType: LSGControlCenter
2025-08-26 16:08:45.456847: LSGate device opened successfully, handle: 1673073161888
2025-08-26 16:08:45.456847: open reader readerType ：22 ret：0
2025-08-26 16:08:45.456847: [[22, 0]]
2025-08-26 16:08:45.456847: changeType:ReaderErrorType.openSuccess
2025-08-26 16:08:45.457844: subThread :ReaderCommand.untilDetected
2025-08-26 16:08:45.457844: commandRsp:ReaderCommand.untilDetected
2025-08-26 16:08:45.457844: subThread :ReaderCommand.startInventory
2025-08-26 16:08:45.457844: commandRsp:ReaderCommand.startInventory
2025-08-26 16:08:45.556518: 🔄 执行首次轮询...
2025-08-26 16:08:45.557515: 🔄 开始RFID轮询检查...
2025-08-26 16:08:45.557515: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:45.557515: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:45.956421: 🔄 开始RFID轮询检查...
2025-08-26 16:08:45.956940: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:45.956940: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:46.061428: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:46.062419:   - 设备句柄: 1673073161888
2025-08-26 16:08:46.063413:   - FetchRecords返回值: 0
2025-08-26 16:08:46.063413:   - 报告数量: 0
2025-08-26 16:08:46.064410: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:46.064410:   - 发现标签数量: 0
2025-08-26 16:08:46.065406:   - 未发现任何RFID标签
2025-08-26 16:08:46.065406: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:46.066403: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:46.456112: 🔍 验证数据收集状态...
2025-08-26 16:08:46.456112: 📊 当前tagList: 0个标签
2025-08-26 16:08:46.457108: ⚠️ tagList为空，等待RFID硬件扫描到标签
2025-08-26 16:08:46.457108: ✅ 主机持续数据收集已启动，共享池将持续接收RFID数据
2025-08-26 16:08:46.457108: 🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失
2025-08-26 16:08:46.458104: 开始全局初始化共享扫描池服务...
2025-08-26 16:08:46.458104: 共享扫描池已集成现有RFID服务
2025-08-26 16:08:46.458104: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 16:08:46.458104: 🔄 开始重置已处理条码集合...
2025-08-26 16:08:46.459101: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:08:46.459101: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:08:46.459101: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:08:46.459101: 📊 当前tagList状态: 0个标签
2025-08-26 16:08:46.459101: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:08:46.460097: 🔄 开始RFID轮询检查...
2025-08-26 16:08:46.460097: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:46.460097: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:46.460097: 🔄 开始RFID轮询检查...
2025-08-26 16:08:46.460097: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:46.460097: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:46.461094: 共享扫描池服务全局初始化完成
2025-08-26 16:08:46.461094: 🚀 初始化新架构服务...
2025-08-26 16:08:46.461094: 🖥️ 主机模式：使用主机集合A服务
2025-08-26 16:08:46.461094: ⏹️ 书籍信息查询服务停止监听
2025-08-26 16:08:46.461094: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-26 16:08:46.461094: ✅ 新架构服务初始化完成
2025-08-26 16:08:46.461094: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-26 16:08:46.462090: 闸机协调器初始化完成
2025-08-26 16:08:46.462090: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-26 16:08:46.462090: 开始初始化主从机扩展...
2025-08-26 16:08:46.462090: 从 seasetting 数据库加载主从机配置成功: channel_1
2025-08-26 16:08:46.462090: 配置详情: 主机模式
2025-08-26 16:08:46.463087: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-26 16:08:46.463087: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-26 16:08:46.463087: 启用主从机扩展: channel_1 (主机)
2025-08-26 16:08:46.463087: ✅ 数据变化通知流已创建
2025-08-26 16:08:46.463087: 共享扫描池已集成现有RFID服务
2025-08-26 16:08:46.463087: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 16:08:46.463087: 🔄 开始重置已处理条码集合...
2025-08-26 16:08:46.463087: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:08:46.464083: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:08:46.464083: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:08:46.464083: 📊 当前tagList状态: 0个标签
2025-08-26 16:08:46.464083: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:08:46.464083: 🔄 开始RFID轮询检查...
2025-08-26 16:08:46.464083: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:46.464083: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:46.464083: 配置为主机模式，监听端口: 8888
2025-08-26 16:08:46.465080: 主机服务器启动成功，监听端口: 8888
2025-08-26 16:08:46.465080: 主机模式配置完成（请求-响应模式）
2025-08-26 16:08:46.465080: [channel_1] 已集成现有GateCoordinator，开始监听事件
2025-08-26 16:08:46.465080: 主从机扩展启用成功
2025-08-26 16:08:46.466083: 主从机扩展初始化完成
2025-08-26 16:08:46.466083: 配置信息: MasterSlaveConfig(channelId: channel_1, isMaster: true, slaveAddress: null, masterAddress: null, port: 8888)
2025-08-26 16:08:46.466083: ✅ 加载到持久化配置: 主机模式, 通道: channel_1
2025-08-26 16:08:46.466083: 主从机扩展初始化完成
2025-08-26 16:08:46.466083: 安全闸机系统初始化完成
2025-08-26 16:08:46.466083: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:46.466083:   - 设备句柄: 1673073161888
2025-08-26 16:08:46.467073:   - FetchRecords返回值: 0
2025-08-26 16:08:46.467073:   - 报告数量: 0
2025-08-26 16:08:46.467073: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:46.467073:   - 发现标签数量: 0
2025-08-26 16:08:46.467073:   - 未发现任何RFID标签
2025-08-26 16:08:46.467073: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:46.467073: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:46.476048: 开始初始化MultiAuthManager...
2025-08-26 16:08:46.476048: 多认证管理器状态变更: initializing
2025-08-26 16:08:46.476048: 认证优先级管理器: 开始加载认证方式
2025-08-26 16:08:46.477041: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 16:08:46.477041: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 16:08:46.477041: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-26 16:08:46.477041: 认证优先级管理器: 最终排序结果: 读者证
2025-08-26 16:08:46.477041: 认证优先级管理器: 主要认证方式: 读者证
2025-08-26 16:08:46.477041: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-26 16:08:46.477041: 多认证管理器: 当前默认显示方式: 读者证
2025-08-26 16:08:46.478039: 初始化读卡器认证服务
2025-08-26 16:08:46.478039: 读卡器认证服务初始化成功
2025-08-26 16:08:46.478039: 初始化共享读卡器认证服务
2025-08-26 16:08:46.478039: 读者证 认证服务初始化成功
2025-08-26 16:08:46.478039: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-26 16:08:46.478039: 多认证管理器状态变更: idle
2025-08-26 16:08:46.479034: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-26 16:08:46.479034: MultiAuthManager初始化完成
2025-08-26 16:08:46.479034: 开始初始化SilencePageViewModel...
2025-08-26 16:08:46.479034: 闸机串口服务已经初始化
2025-08-26 16:08:46.479034: 开始初始化闸机认证服务...
2025-08-26 16:08:46.479034: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-26 16:08:46.479034: RFID服务已经初始化
2025-08-26 16:08:46.479034: SIP2图书信息服务初始化完成
2025-08-26 16:08:46.480030: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-26 16:08:46.480030: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-26 16:08:46.480030: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-26 16:08:46.480030: 串口监听已经启动
2025-08-26 16:08:46.480030: SilencePageViewModel初始化完成
2025-08-26 16:08:46.819904: dispose IndexPage
2025-08-26 16:08:46.820901: IndexPage dispose
2025-08-26 16:08:46.955455: 🔄 开始RFID轮询检查...
2025-08-26 16:08:46.956452: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:46.956452: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:46.956452: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:46.957449:   - 设备句柄: 1673073161888
2025-08-26 16:08:46.957449:   - FetchRecords返回值: 0
2025-08-26 16:08:46.958446:   - 报告数量: 0
2025-08-26 16:08:46.958446: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:46.958446:   - 发现标签数量: 0
2025-08-26 16:08:46.959443:   - 未发现任何RFID标签
2025-08-26 16:08:46.959443: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:46.959443: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:46.981368: 🔍 主从机模式检测: 启用=true, 主机模式=true
2025-08-26 16:08:46.981368: 🔍 扩展详细状态: {enabled: true, channel_id: channel_1, is_master: true, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: false, timestamp: 2025-08-26T16:08:46.981368}
2025-08-26 16:08:46.981368: 🎯 检测到主机模式，无需设置数据监听
2025-08-26 16:08:47.455335: 🔄 开始RFID轮询检查...
2025-08-26 16:08:47.456335: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:47.457329: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:47.457329: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:47.458326:   - 设备句柄: 1673073161888
2025-08-26 16:08:47.458326:   - FetchRecords返回值: 0
2025-08-26 16:08:47.458326:   - 报告数量: 0
2025-08-26 16:08:47.458326: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:47.459322:   - 发现标签数量: 0
2025-08-26 16:08:47.459322:   - 未发现任何RFID标签
2025-08-26 16:08:47.459322: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:47.459322: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:47.956673: 🔄 开始RFID轮询检查...
2025-08-26 16:08:47.957670: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:47.957670: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:47.958667: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:47.958667:   - 设备句柄: 1673073161888
2025-08-26 16:08:47.958667:   - FetchRecords返回值: 0
2025-08-26 16:08:47.959663:   - 报告数量: 0
2025-08-26 16:08:47.959663: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:47.960660:   - 发现标签数量: 0
2025-08-26 16:08:47.960660:   - 未发现任何RFID标签
2025-08-26 16:08:47.960660: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:47.961656: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:48.457013: 🔄 开始RFID轮询检查...
2025-08-26 16:08:48.457013: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:48.458011: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:48.458011: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:48.459007:   - 设备句柄: 1673073161888
2025-08-26 16:08:48.459007:   - FetchRecords返回值: 0
2025-08-26 16:08:48.460004:   - 报告数量: 0
2025-08-26 16:08:48.460004: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:48.461002:   - 发现标签数量: 0
2025-08-26 16:08:48.462002:   - 未发现任何RFID标签
2025-08-26 16:08:48.462994: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:48.462994: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:48.956358: 🔄 开始RFID轮询检查...
2025-08-26 16:08:48.956358: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:48.957355: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:48.957355: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:48.958351:   - 设备句柄: 1673073161888
2025-08-26 16:08:48.958351:   - FetchRecords返回值: 0
2025-08-26 16:08:48.959348:   - 报告数量: 0
2025-08-26 16:08:48.959348: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:48.959348:   - 发现标签数量: 0
2025-08-26 16:08:48.960344:   - 未发现任何RFID标签
2025-08-26 16:08:48.960344: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:48.960344: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:49.455702: 🔄 开始RFID轮询检查...
2025-08-26 16:08:49.456700: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:49.456700: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:49.458693: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:49.458693:   - 设备句柄: 1673073161888
2025-08-26 16:08:49.459690:   - FetchRecords返回值: 0
2025-08-26 16:08:49.459690:   - 报告数量: 0
2025-08-26 16:08:49.460686: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:49.460686:   - 发现标签数量: 0
2025-08-26 16:08:49.460686:   - 未发现任何RFID标签
2025-08-26 16:08:49.460686: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:49.461681: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:49.956042: 🔄 开始RFID轮询检查...
2025-08-26 16:08:49.956042: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:49.956042: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:49.957040: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:49.957040:   - 设备句柄: 1673073161888
2025-08-26 16:08:49.957040:   - FetchRecords返回值: 0
2025-08-26 16:08:49.957040:   - 报告数量: 0
2025-08-26 16:08:49.958036: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:49.958036:   - 发现标签数量: 0
2025-08-26 16:08:49.958036:   - 未发现任何RFID标签
2025-08-26 16:08:49.958036: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:49.958036: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:50.455928: 🔄 开始RFID轮询检查...
2025-08-26 16:08:50.455928: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:50.456925: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:50.456925: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:50.456925:   - 设备句柄: 1673073161888
2025-08-26 16:08:50.457922:   - FetchRecords返回值: 0
2025-08-26 16:08:50.457922:   - 报告数量: 0
2025-08-26 16:08:50.457922: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:50.458918:   - 发现标签数量: 0
2025-08-26 16:08:50.458918:   - 未发现任何RFID标签
2025-08-26 16:08:50.458918: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:50.459915: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:50.955272: 🔄 开始RFID轮询检查...
2025-08-26 16:08:50.955272: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:50.956270: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:50.956270: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:50.957266:   - 设备句柄: 1673073161888
2025-08-26 16:08:50.957266:   - FetchRecords返回值: 0
2025-08-26 16:08:50.957266:   - 报告数量: 0
2025-08-26 16:08:50.958262: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:50.958262:   - 发现标签数量: 0
2025-08-26 16:08:50.958262:   - 未发现任何RFID标签
2025-08-26 16:08:50.959259: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:50.959259: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:51.455613: 🔄 开始RFID轮询检查...
2025-08-26 16:08:51.455613: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:51.455613: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:51.456611: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:51.456611:   - 设备句柄: 1673073161888
2025-08-26 16:08:51.457607:   - FetchRecords返回值: 0
2025-08-26 16:08:51.457607:   - 报告数量: 0
2025-08-26 16:08:51.457607: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:51.458603:   - 发现标签数量: 0
2025-08-26 16:08:51.458603:   - 未发现任何RFID标签
2025-08-26 16:08:51.458603: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:51.459600: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:51.955954: 🔄 开始RFID轮询检查...
2025-08-26 16:08:51.955954: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:51.956951: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:51.956951: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:51.956951:   - 设备句柄: 1673073161888
2025-08-26 16:08:51.957948:   - FetchRecords返回值: 0
2025-08-26 16:08:51.957948:   - 报告数量: 0
2025-08-26 16:08:51.957948: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:51.958944:   - 发现标签数量: 0
2025-08-26 16:08:51.958944:   - 未发现任何RFID标签
2025-08-26 16:08:51.958944: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:51.959941: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:52.455298: 🔄 开始RFID轮询检查...
2025-08-26 16:08:52.455298: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:52.456295: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:52.456295: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:52.456295:   - 设备句柄: 1673073161888
2025-08-26 16:08:52.457292:   - FetchRecords返回值: 0
2025-08-26 16:08:52.457292:   - 报告数量: 0
2025-08-26 16:08:52.457292: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:52.458288:   - 发现标签数量: 0
2025-08-26 16:08:52.458288:   - 未发现任何RFID标签
2025-08-26 16:08:52.458288: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:52.459285: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:52.955639: 🔄 开始RFID轮询检查...
2025-08-26 16:08:52.955639: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:52.956637: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:52.956637: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:52.957633:   - 设备句柄: 1673073161888
2025-08-26 16:08:52.957633:   - FetchRecords返回值: 0
2025-08-26 16:08:52.957633:   - 报告数量: 0
2025-08-26 16:08:52.958629: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:52.958629:   - 发现标签数量: 0
2025-08-26 16:08:52.958629:   - 未发现任何RFID标签
2025-08-26 16:08:52.958629: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:52.959626: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:53.455512: 🔄 开始RFID轮询检查...
2025-08-26 16:08:53.455512: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:53.456512: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:53.456512: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:53.456512:   - 设备句柄: 1673073161888
2025-08-26 16:08:53.457507:   - FetchRecords返回值: 0
2025-08-26 16:08:53.457507:   - 报告数量: 0
2025-08-26 16:08:53.457507: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:53.458502:   - 发现标签数量: 0
2025-08-26 16:08:53.458502:   - 未发现任何RFID标签
2025-08-26 16:08:53.458502: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:53.459499: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:53.955853: 🔄 开始RFID轮询检查...
2025-08-26 16:08:53.955853: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:53.956851: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:53.956851: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:53.957847:   - 设备句柄: 1673073161888
2025-08-26 16:08:53.957847:   - FetchRecords返回值: 0
2025-08-26 16:08:53.958844:   - 报告数量: 0
2025-08-26 16:08:53.958844: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:53.958844:   - 发现标签数量: 0
2025-08-26 16:08:53.959840:   - 未发现任何RFID标签
2025-08-26 16:08:53.959840: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:53.960838: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:54.455197: 🔄 开始RFID轮询检查...
2025-08-26 16:08:54.455197: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:54.456195: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:54.456195: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:54.457191:   - 设备句柄: 1673073161888
2025-08-26 16:08:54.457191:   - FetchRecords返回值: 0
2025-08-26 16:08:54.457191:   - 报告数量: 0
2025-08-26 16:08:54.458188: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:54.458188:   - 发现标签数量: 0
2025-08-26 16:08:54.458188:   - 未发现任何RFID标签
2025-08-26 16:08:54.459184: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:54.459184: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:54.955538: 🔄 开始RFID轮询检查...
2025-08-26 16:08:54.955538: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:54.956535: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:54.956535: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:54.956535:   - 设备句柄: 1673073161888
2025-08-26 16:08:54.957532:   - FetchRecords返回值: 0
2025-08-26 16:08:54.957532:   - 报告数量: 0
2025-08-26 16:08:54.957532: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:54.958528:   - 发现标签数量: 0
2025-08-26 16:08:54.958528:   - 未发现任何RFID标签
2025-08-26 16:08:54.958528: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:54.959525: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:55.455940: 🔄 开始RFID轮询检查...
2025-08-26 16:08:55.455940: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:55.456947: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:55.456947: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:55.456947:   - 设备句柄: 1673073161888
2025-08-26 16:08:55.457934:   - FetchRecords返回值: 0
2025-08-26 16:08:55.457934:   - 报告数量: 0
2025-08-26 16:08:55.457934: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:55.458932:   - 发现标签数量: 0
2025-08-26 16:08:55.458932:   - 未发现任何RFID标签
2025-08-26 16:08:55.459926: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:55.459926: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:55.956281: 🔄 开始RFID轮询检查...
2025-08-26 16:08:55.956281: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:55.957278: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:55.957278: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:55.957278:   - 设备句柄: 1673073161888
2025-08-26 16:08:55.958274:   - FetchRecords返回值: 0
2025-08-26 16:08:55.958274:   - 报告数量: 0
2025-08-26 16:08:55.958274: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:55.959271:   - 发现标签数量: 0
2025-08-26 16:08:55.959271:   - 未发现任何RFID标签
2025-08-26 16:08:55.959271: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:55.960267: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:56.456170: 🔄 开始RFID轮询检查...
2025-08-26 16:08:56.456170: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:56.457166: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:56.459160: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:56.460157:   - 设备句柄: 1673073161888
2025-08-26 16:08:56.460157:   - FetchRecords返回值: 0
2025-08-26 16:08:56.460157:   - 报告数量: 0
2025-08-26 16:08:56.461153: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:56.461153:   - 发现标签数量: 0
2025-08-26 16:08:56.461153:   - 未发现任何RFID标签
2025-08-26 16:08:56.461153: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:56.462150: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:56.955514: 🔄 开始RFID轮询检查...
2025-08-26 16:08:56.955514: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:56.956511: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:56.956511: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:56.956511:   - 设备句柄: 1673073161888
2025-08-26 16:08:56.957508:   - FetchRecords返回值: 0
2025-08-26 16:08:56.957508:   - 报告数量: 0
2025-08-26 16:08:56.957508: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:56.958504:   - 发现标签数量: 0
2025-08-26 16:08:56.958504:   - 未发现任何RFID标签
2025-08-26 16:08:56.958504: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:56.959500: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:57.455855: 🔄 开始RFID轮询检查...
2025-08-26 16:08:57.455855: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:57.456852: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:57.456852: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:57.456852:   - 设备句柄: 1673073161888
2025-08-26 16:08:57.457849:   - FetchRecords返回值: 0
2025-08-26 16:08:57.457849:   - 报告数量: 0
2025-08-26 16:08:57.457849: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:57.458845:   - 发现标签数量: 0
2025-08-26 16:08:57.458845:   - 未发现任何RFID标签
2025-08-26 16:08:57.458845: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:57.459842: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:57.956195: 🔄 开始RFID轮询检查...
2025-08-26 16:08:57.956195: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:57.957193: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:57.957193: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:57.957193:   - 设备句柄: 1673073161888
2025-08-26 16:08:57.958189:   - FetchRecords返回值: 0
2025-08-26 16:08:57.958189:   - 报告数量: 0
2025-08-26 16:08:57.958189: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:57.959186:   - 发现标签数量: 0
2025-08-26 16:08:57.959186:   - 未发现任何RFID标签
2025-08-26 16:08:57.959186: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:57.960182: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:58.455540: 🔄 开始RFID轮询检查...
2025-08-26 16:08:58.455540: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:58.456537: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:58.456537: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:58.457534:   - 设备句柄: 1673073161888
2025-08-26 16:08:58.457534:   - FetchRecords返回值: 0
2025-08-26 16:08:58.457534:   - 报告数量: 0
2025-08-26 16:08:58.458530: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:58.458530:   - 发现标签数量: 0
2025-08-26 16:08:58.458530:   - 未发现任何RFID标签
2025-08-26 16:08:58.458530: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:58.459526: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:58.956877: 🔄 开始RFID轮询检查...
2025-08-26 16:08:58.956877: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:58.957875: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:58.957875: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:58.958871:   - 设备句柄: 1673073161888
2025-08-26 16:08:58.958871:   - FetchRecords返回值: 0
2025-08-26 16:08:58.958871:   - 报告数量: 0
2025-08-26 16:08:58.958871: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:58.959867:   - 发现标签数量: 0
2025-08-26 16:08:58.959867:   - 未发现任何RFID标签
2025-08-26 16:08:58.959867: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:58.960864: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:59.455753: 🔄 开始RFID轮询检查...
2025-08-26 16:08:59.455753: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:59.455753: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:59.456751: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:59.456751:   - 设备句柄: 1673073161888
2025-08-26 16:08:59.457748:   - FetchRecords返回值: 0
2025-08-26 16:08:59.457748:   - 报告数量: 0
2025-08-26 16:08:59.457748: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:59.458744:   - 发现标签数量: 0
2025-08-26 16:08:59.458744:   - 未发现任何RFID标签
2025-08-26 16:08:59.458744: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:59.459741: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:08:59.956620: 🔄 开始RFID轮询检查...
2025-08-26 16:08:59.956620: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:08:59.957617: ⚠️ tagList为空，场上无标签
2025-08-26 16:08:59.957617: 🔍 LSGate硬件扫描详情:
2025-08-26 16:08:59.958614:   - 设备句柄: 1673073161888
2025-08-26 16:08:59.958614:   - FetchRecords返回值: 0
2025-08-26 16:08:59.958614:   - 报告数量: 0
2025-08-26 16:08:59.959611: 📊 LSGate扫描结果汇总:
2025-08-26 16:08:59.959611:   - 发现标签数量: 0
2025-08-26 16:08:59.959611:   - 未发现任何RFID标签
2025-08-26 16:08:59.960607: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:08:59.960607: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:00.455964: 🔄 开始RFID轮询检查...
2025-08-26 16:09:00.455964: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:00.456962: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:00.456962: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:00.457959:   - 设备句柄: 1673073161888
2025-08-26 16:09:00.457959:   - FetchRecords返回值: 0
2025-08-26 16:09:00.457959:   - 报告数量: 0
2025-08-26 16:09:00.457959: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:00.458954:   - 发现标签数量: 0
2025-08-26 16:09:00.458954:   - 未发现任何RFID标签
2025-08-26 16:09:00.458954: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:00.459951: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:00.956305: 🔄 开始RFID轮询检查...
2025-08-26 16:09:00.956305: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:00.957302: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:00.957302: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:00.957302:   - 设备句柄: 1673073161888
2025-08-26 16:09:00.958299:   - FetchRecords返回值: 0
2025-08-26 16:09:00.958299:   - 报告数量: 0
2025-08-26 16:09:00.958299: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:00.959295:   - 发现标签数量: 0
2025-08-26 16:09:00.959295:   - 未发现任何RFID标签
2025-08-26 16:09:00.959295: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:00.960292: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:01.455649: 🔄 开始RFID轮询检查...
2025-08-26 16:09:01.455649: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:01.456647: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:01.456647: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:01.456647:   - 设备句柄: 1673073161888
2025-08-26 16:09:01.457643:   - FetchRecords返回值: 0
2025-08-26 16:09:01.457643:   - 报告数量: 0
2025-08-26 16:09:01.457643: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:01.458639:   - 发现标签数量: 0
2025-08-26 16:09:01.458639:   - 未发现任何RFID标签
2025-08-26 16:09:01.458639: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:01.459636: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:01.955990: 🔄 开始RFID轮询检查...
2025-08-26 16:09:01.955990: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:01.956987: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:01.956987: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:01.956987:   - 设备句柄: 1673073161888
2025-08-26 16:09:01.957984:   - FetchRecords返回值: 0
2025-08-26 16:09:01.957984:   - 报告数量: 0
2025-08-26 16:09:01.957984: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:01.958980:   - 发现标签数量: 0
2025-08-26 16:09:01.958980:   - 未发现任何RFID标签
2025-08-26 16:09:01.958980: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:01.959977: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:02.455875: 🔄 开始RFID轮询检查...
2025-08-26 16:09:02.455875: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:02.456872: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:02.456872: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:02.456872:   - 设备句柄: 1673073161888
2025-08-26 16:09:02.457869:   - FetchRecords返回值: 0
2025-08-26 16:09:02.457869:   - 报告数量: 0
2025-08-26 16:09:02.457869: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:02.458865:   - 发现标签数量: 0
2025-08-26 16:09:02.458865:   - 未发现任何RFID标签
2025-08-26 16:09:02.458865: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:02.459862: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:02.956215: 🔄 开始RFID轮询检查...
2025-08-26 16:09:02.956215: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:02.957213: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:02.957213: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:02.957213:   - 设备句柄: 1673073161888
2025-08-26 16:09:02.958210:   - FetchRecords返回值: 0
2025-08-26 16:09:02.958210:   - 报告数量: 0
2025-08-26 16:09:02.958210: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:02.959206:   - 发现标签数量: 0
2025-08-26 16:09:02.959206:   - 未发现任何RFID标签
2025-08-26 16:09:02.959206: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:02.960203: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:03.455560: 🔄 开始RFID轮询检查...
2025-08-26 16:09:03.455560: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:03.456557: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:03.456557: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:03.457555:   - 设备句柄: 1673073161888
2025-08-26 16:09:03.457555:   - FetchRecords返回值: 0
2025-08-26 16:09:03.457555:   - 报告数量: 0
2025-08-26 16:09:03.458551: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:03.458551:   - 发现标签数量: 0
2025-08-26 16:09:03.458551:   - 未发现任何RFID标签
2025-08-26 16:09:03.459547: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:03.459547: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:03.955900: 🔄 开始RFID轮询检查...
2025-08-26 16:09:03.955900: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:03.956898: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:03.956898: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:03.956898:   - 设备句柄: 1673073161888
2025-08-26 16:09:03.957895:   - FetchRecords返回值: 0
2025-08-26 16:09:03.957895:   - 报告数量: 0
2025-08-26 16:09:03.957895: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:03.958891:   - 发现标签数量: 0
2025-08-26 16:09:03.958891:   - 未发现任何RFID标签
2025-08-26 16:09:03.958891: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:03.959887: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:04.455245: 🔄 开始RFID轮询检查...
2025-08-26 16:09:04.455245: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:04.456245: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:04.456245: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:04.457239:   - 设备句柄: 1673073161888
2025-08-26 16:09:04.457239:   - FetchRecords返回值: 0
2025-08-26 16:09:04.457239:   - 报告数量: 0
2025-08-26 16:09:04.457239: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:04.458235:   - 发现标签数量: 0
2025-08-26 16:09:04.458235:   - 未发现任何RFID标签
2025-08-26 16:09:04.458235: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:04.459232: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:04.955586: 🔄 开始RFID轮询检查...
2025-08-26 16:09:04.955586: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:04.956584: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:04.956584: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:04.957580:   - 设备句柄: 1673073161888
2025-08-26 16:09:04.957580:   - FetchRecords返回值: 0
2025-08-26 16:09:04.957580:   - 报告数量: 0
2025-08-26 16:09:04.958576: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:04.958576:   - 发现标签数量: 0
2025-08-26 16:09:04.958576:   - 未发现任何RFID标签
2025-08-26 16:09:04.959573: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:04.959573: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:05.456521: 🔄 开始RFID轮询检查...
2025-08-26 16:09:05.456521: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:05.457519: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:05.457519: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:05.458516:   - 设备句柄: 1673073161888
2025-08-26 16:09:05.458516:   - FetchRecords返回值: 0
2025-08-26 16:09:05.458516:   - 报告数量: 0
2025-08-26 16:09:05.459512: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:05.459512:   - 发现标签数量: 0
2025-08-26 16:09:05.459512:   - 未发现任何RFID标签
2025-08-26 16:09:05.459512: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:05.460508: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:05.955865: 🔄 开始RFID轮询检查...
2025-08-26 16:09:05.955865: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:05.956863: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:05.956863: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:05.957860:   - 设备句柄: 1673073161888
2025-08-26 16:09:05.957860:   - FetchRecords返回值: 0
2025-08-26 16:09:05.957860:   - 报告数量: 0
2025-08-26 16:09:05.958856: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:05.958856:   - 发现标签数量: 0
2025-08-26 16:09:05.958856:   - 未发现任何RFID标签
2025-08-26 16:09:05.959853: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:05.959853: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:06.455210: 🔄 开始RFID轮询检查...
2025-08-26 16:09:06.455210: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:06.456207: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:06.456207: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:06.457203:   - 设备句柄: 1673073161888
2025-08-26 16:09:06.457203:   - FetchRecords返回值: 0
2025-08-26 16:09:06.457203:   - 报告数量: 0
2025-08-26 16:09:06.458200: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:06.458200:   - 发现标签数量: 0
2025-08-26 16:09:06.458200:   - 未发现任何RFID标签
2025-08-26 16:09:06.459196: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:06.459196: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:06.955550: 🔄 开始RFID轮询检查...
2025-08-26 16:09:06.955550: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:06.955550: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:06.956548: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:06.956548:   - 设备句柄: 1673073161888
2025-08-26 16:09:06.957544:   - FetchRecords返回值: 0
2025-08-26 16:09:06.957544:   - 报告数量: 0
2025-08-26 16:09:06.957544: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:06.958541:   - 发现标签数量: 0
2025-08-26 16:09:06.958541:   - 未发现任何RFID标签
2025-08-26 16:09:06.958541: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:06.958541: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:07.455891: 🔄 开始RFID轮询检查...
2025-08-26 16:09:07.455891: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:07.456889: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:07.457886: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:07.457886:   - 设备句柄: 1673073161888
2025-08-26 16:09:07.458882:   - FetchRecords返回值: 0
2025-08-26 16:09:07.458882:   - 报告数量: 0
2025-08-26 16:09:07.458882: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:07.459879:   - 发现标签数量: 0
2025-08-26 16:09:07.459879:   - 未发现任何RFID标签
2025-08-26 16:09:07.459879: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:07.459879: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:07.956232: 🔄 开始RFID轮询检查...
2025-08-26 16:09:07.956232: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:07.957229: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:07.957229: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:07.957229:   - 设备句柄: 1673073161888
2025-08-26 16:09:07.958226:   - FetchRecords返回值: 0
2025-08-26 16:09:07.958226:   - 报告数量: 0
2025-08-26 16:09:07.958226: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:07.959222:   - 发现标签数量: 0
2025-08-26 16:09:07.959222:   - 未发现任何RFID标签
2025-08-26 16:09:07.959222: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:07.960219: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:08.455666: 🔄 开始RFID轮询检查...
2025-08-26 16:09:08.455666: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:08.456663: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:08.456663: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:08.456663:   - 设备句柄: 1673073161888
2025-08-26 16:09:08.457660:   - FetchRecords返回值: 0
2025-08-26 16:09:08.457660:   - 报告数量: 0
2025-08-26 16:09:08.457660: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:08.458657:   - 发现标签数量: 0
2025-08-26 16:09:08.458657:   - 未发现任何RFID标签
2025-08-26 16:09:08.458657: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:08.459654: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:08.956007: 🔄 开始RFID轮询检查...
2025-08-26 16:09:08.956007: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:08.957006: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:08.957006: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:08.958002:   - 设备句柄: 1673073161888
2025-08-26 16:09:08.958002:   - FetchRecords返回值: 0
2025-08-26 16:09:08.958002:   - 报告数量: 0
2025-08-26 16:09:08.958998: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:08.958998:   - 发现标签数量: 0
2025-08-26 16:09:08.958998:   - 未发现任何RFID标签
2025-08-26 16:09:08.959994: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:08.959994: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:09.455352: 🔄 开始RFID轮询检查...
2025-08-26 16:09:09.455352: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:09.456349: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:09.456349: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:09.456349:   - 设备句柄: 1673073161888
2025-08-26 16:09:09.457346:   - FetchRecords返回值: 0
2025-08-26 16:09:09.457346:   - 报告数量: 0
2025-08-26 16:09:09.457346: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:09.458342:   - 发现标签数量: 0
2025-08-26 16:09:09.458342:   - 未发现任何RFID标签
2025-08-26 16:09:09.458342: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:09.459339: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:09.955692: 🔄 开始RFID轮询检查...
2025-08-26 16:09:09.955692: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:09.956690: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:09.956690: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:09.957686:   - 设备句柄: 1673073161888
2025-08-26 16:09:09.957686:   - FetchRecords返回值: 0
2025-08-26 16:09:09.957686:   - 报告数量: 0
2025-08-26 16:09:09.957686: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:09.958683:   - 发现标签数量: 0
2025-08-26 16:09:09.958683:   - 未发现任何RFID标签
2025-08-26 16:09:09.959680: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:09.959680: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:10.455036: 🔄 开始RFID轮询检查...
2025-08-26 16:09:10.455036: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:10.456034: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:10.456034: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:10.456034:   - 设备句柄: 1673073161888
2025-08-26 16:09:10.457030:   - FetchRecords返回值: 0
2025-08-26 16:09:10.457030:   - 报告数量: 0
2025-08-26 16:09:10.457030: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:10.458027:   - 发现标签数量: 0
2025-08-26 16:09:10.458027:   - 未发现任何RFID标签
2025-08-26 16:09:10.458027: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:10.458027: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:10.955377: 🔄 开始RFID轮询检查...
2025-08-26 16:09:10.955377: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:10.956375: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:10.956375: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:10.956375:   - 设备句柄: 1673073161888
2025-08-26 16:09:10.957371:   - FetchRecords返回值: 0
2025-08-26 16:09:10.957371:   - 报告数量: 0
2025-08-26 16:09:10.957371: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:10.958367:   - 发现标签数量: 0
2025-08-26 16:09:10.958367:   - 未发现任何RFID标签
2025-08-26 16:09:10.958367: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:10.959364: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:11.456251: 🔄 开始RFID轮询检查...
2025-08-26 16:09:11.456251: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:11.457249: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:11.457249: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:11.458245:   - 设备句柄: 1673073161888
2025-08-26 16:09:11.458245:   - FetchRecords返回值: 0
2025-08-26 16:09:11.458245:   - 报告数量: 0
2025-08-26 16:09:11.459241: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:11.459241:   - 发现标签数量: 0
2025-08-26 16:09:11.459241:   - 未发现任何RFID标签
2025-08-26 16:09:11.460238: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:11.460238: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:11.955595: 🔄 开始RFID轮询检查...
2025-08-26 16:09:11.955595: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:11.956593: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:11.956593: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:11.956593:   - 设备句柄: 1673073161888
2025-08-26 16:09:11.957589:   - FetchRecords返回值: 0
2025-08-26 16:09:11.957589:   - 报告数量: 0
2025-08-26 16:09:11.957589: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:11.958585:   - 发现标签数量: 0
2025-08-26 16:09:11.958585:   - 未发现任何RFID标签
2025-08-26 16:09:11.958585: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:11.959582: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:12.455936: 🔄 开始RFID轮询检查...
2025-08-26 16:09:12.455936: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:12.455936: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:12.456933: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:12.456933:   - 设备句柄: 1673073161888
2025-08-26 16:09:12.457930:   - FetchRecords返回值: 0
2025-08-26 16:09:12.457930:   - 报告数量: 0
2025-08-26 16:09:12.457930: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:12.458926:   - 发现标签数量: 0
2025-08-26 16:09:12.458926:   - 未发现任何RFID标签
2025-08-26 16:09:12.458926: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:12.459923: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:12.956277: 🔄 开始RFID轮询检查...
2025-08-26 16:09:12.956277: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:12.957274: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:12.957274: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:12.957274:   - 设备句柄: 1673073161888
2025-08-26 16:09:12.958271:   - FetchRecords返回值: 0
2025-08-26 16:09:12.958271:   - 报告数量: 0
2025-08-26 16:09:12.958271: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:12.959267:   - 发现标签数量: 0
2025-08-26 16:09:12.959267:   - 未发现任何RFID标签
2025-08-26 16:09:12.959267: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:12.960264: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:13.455621: 🔄 开始RFID轮询检查...
2025-08-26 16:09:13.455621: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:13.456618: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:13.456618: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:13.456618:   - 设备句柄: 1673073161888
2025-08-26 16:09:13.457615:   - FetchRecords返回值: 0
2025-08-26 16:09:13.457615:   - 报告数量: 0
2025-08-26 16:09:13.457615: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:13.458612:   - 发现标签数量: 0
2025-08-26 16:09:13.458612:   - 未发现任何RFID标签
2025-08-26 16:09:13.458612: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:13.459608: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:13.955962: 🔄 开始RFID轮询检查...
2025-08-26 16:09:13.955962: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:13.956960: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:13.956960: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:13.957955:   - 设备句柄: 1673073161888
2025-08-26 16:09:13.957955:   - FetchRecords返回值: 0
2025-08-26 16:09:13.957955:   - 报告数量: 0
2025-08-26 16:09:13.958953: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:13.958953:   - 发现标签数量: 0
2025-08-26 16:09:13.958953:   - 未发现任何RFID标签
2025-08-26 16:09:13.958953: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:13.959949: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:14.455836: 🔄 开始RFID轮询检查...
2025-08-26 16:09:14.455836: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:14.456834: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:14.456834: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:14.456834:   - 设备句柄: 1673073161888
2025-08-26 16:09:14.457830:   - FetchRecords返回值: 0
2025-08-26 16:09:14.457830:   - 报告数量: 0
2025-08-26 16:09:14.457830: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:14.458827:   - 发现标签数量: 0
2025-08-26 16:09:14.458827:   - 未发现任何RFID标签
2025-08-26 16:09:14.458827: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:14.459823: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:14.956177: 🔄 开始RFID轮询检查...
2025-08-26 16:09:14.956177: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:14.957176: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:14.957176: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:14.958174:   - 设备句柄: 1673073161888
2025-08-26 16:09:14.958174:   - FetchRecords返回值: 0
2025-08-26 16:09:14.958174:   - 报告数量: 0
2025-08-26 16:09:14.959167: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:14.959167:   - 发现标签数量: 0
2025-08-26 16:09:14.959167:   - 未发现任何RFID标签
2025-08-26 16:09:14.959167: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:14.960164: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:15.456048: 🔄 开始RFID轮询检查...
2025-08-26 16:09:15.456048: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:15.457045: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:15.458050: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:15.458050: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:15.459038: 🔍 数据长度: 8 字节
2025-08-26 16:09:15.459038: 🔍 预定义命令列表:
2025-08-26 16:09:15.459038:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:09:15.459038:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:09:15.460034:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:15.460034:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:15.460034:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:09:15.460034:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:09:15.461031:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:09:15.461031:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:09:15.461031:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:09:15.461031:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:09:15.461031: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 16:09:15.461031: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 16:09:15.461031: 收到闸机命令: exit_end (出馆结束)
2025-08-26 16:09:15.461031: 出馆流程结束
2025-08-26 16:09:15.462027: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 16:09:15.462027: [channel_1] 收到闸机事件: exit_end
2025-08-26 16:09:15.462027: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 16:09:15.462027: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 16:09:15.462027: [channel_1] 处理队列已清空
2025-08-26 16:09:15.462027: 📨 收到GateCoordinator事件: exit_end
2025-08-26 16:09:15.462027: 页面状态变更: SilencePageState.welcome
2025-08-26 16:09:15.462027: [channel_1] 通知收集到的条码: []
2025-08-26 16:09:15.462027: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 16:09:15.463024: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:15.463024:   - 设备句柄: 1673073161888
2025-08-26 16:09:15.463024:   - FetchRecords返回值: 0
2025-08-26 16:09:15.463024:   - 报告数量: 0
2025-08-26 16:09:15.463024: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:15.463024:   - 发现标签数量: 0
2025-08-26 16:09:15.463024:   - 未发现任何RFID标签
2025-08-26 16:09:15.463024: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:15.464020: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:15.956388: 🔄 开始RFID轮询检查...
2025-08-26 16:09:15.956388: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:15.956388: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:15.957385: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:15.957385:   - 设备句柄: 1673073161888
2025-08-26 16:09:15.957385:   - FetchRecords返回值: 0
2025-08-26 16:09:15.957385:   - 报告数量: 0
2025-08-26 16:09:15.958382: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:15.958382:   - 发现标签数量: 0
2025-08-26 16:09:15.958382:   - 未发现任何RFID标签
2025-08-26 16:09:15.958382: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:15.958382: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:16.455732: 🔄 开始RFID轮询检查...
2025-08-26 16:09:16.455732: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:16.455732: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:16.456735: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:16.456735:   - 设备句柄: 1673073161888
2025-08-26 16:09:16.456735:   - FetchRecords返回值: 0
2025-08-26 16:09:16.456735:   - 报告数量: 0
2025-08-26 16:09:16.457725: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:16.457725:   - 发现标签数量: 0
2025-08-26 16:09:16.457725:   - 未发现任何RFID标签
2025-08-26 16:09:16.457725: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:16.457725: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:16.956073: 🔄 开始RFID轮询检查...
2025-08-26 16:09:16.956073: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:16.956073: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:16.957070: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:16.957070:   - 设备句柄: 1673073161888
2025-08-26 16:09:16.957070:   - FetchRecords返回值: 0
2025-08-26 16:09:16.957070:   - 报告数量: 0
2025-08-26 16:09:16.958066: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:16.958066:   - 发现标签数量: 0
2025-08-26 16:09:16.958066:   - 未发现任何RFID标签
2025-08-26 16:09:16.958066: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:16.958066: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:17.455939: 🔄 开始RFID轮询检查...
2025-08-26 16:09:17.455939: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:17.455939: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:17.456935: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:17.456935:   - 设备句柄: 1673073161888
2025-08-26 16:09:17.456935:   - FetchRecords返回值: 0
2025-08-26 16:09:17.456935:   - 报告数量: 0
2025-08-26 16:09:17.456935: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:17.457932:   - 发现标签数量: 0
2025-08-26 16:09:17.457932:   - 未发现任何RFID标签
2025-08-26 16:09:17.457932: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:17.457932: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:17.956279: 🔄 开始RFID轮询检查...
2025-08-26 16:09:17.956279: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:17.956279: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:17.957277: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:17.957277:   - 设备句柄: 1673073161888
2025-08-26 16:09:17.957277:   - FetchRecords返回值: 0
2025-08-26 16:09:17.957277:   - 报告数量: 0
2025-08-26 16:09:17.958273: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:17.958273:   - 发现标签数量: 0
2025-08-26 16:09:17.958273:   - 未发现任何RFID标签
2025-08-26 16:09:17.958273: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:17.958273: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:18.455623: 🔄 开始RFID轮询检查...
2025-08-26 16:09:18.455623: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:18.455623: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:18.456621: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:18.456621:   - 设备句柄: 1673073161888
2025-08-26 16:09:18.456621:   - FetchRecords返回值: 0
2025-08-26 16:09:18.456621:   - 报告数量: 0
2025-08-26 16:09:18.457617: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:18.457617:   - 发现标签数量: 0
2025-08-26 16:09:18.457617:   - 未发现任何RFID标签
2025-08-26 16:09:18.457617: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:18.457617: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:18.955964: 🔄 开始RFID轮询检查...
2025-08-26 16:09:18.955964: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:18.955964: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:18.956961: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:18.956961:   - 设备句柄: 1673073161888
2025-08-26 16:09:18.956961:   - FetchRecords返回值: 0
2025-08-26 16:09:18.956961:   - 报告数量: 0
2025-08-26 16:09:18.957958: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:18.957958:   - 发现标签数量: 0
2025-08-26 16:09:18.957958:   - 未发现任何RFID标签
2025-08-26 16:09:18.957958: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:18.957958: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:19.455308: 🔄 开始RFID轮询检查...
2025-08-26 16:09:19.455308: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:19.455308: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:19.456306: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:19.456306:   - 设备句柄: 1673073161888
2025-08-26 16:09:19.456306:   - FetchRecords返回值: 0
2025-08-26 16:09:19.456306:   - 报告数量: 0
2025-08-26 16:09:19.457302: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:19.457302:   - 发现标签数量: 0
2025-08-26 16:09:19.457302:   - 未发现任何RFID标签
2025-08-26 16:09:19.457302: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:19.457302: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:19.956164: 🔄 开始RFID轮询检查...
2025-08-26 16:09:19.956164: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:19.956164: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:19.957161: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:19.957161:   - 设备句柄: 1673073161888
2025-08-26 16:09:19.957161:   - FetchRecords返回值: 0
2025-08-26 16:09:19.957161:   - 报告数量: 0
2025-08-26 16:09:19.958157: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:19.958157:   - 发现标签数量: 0
2025-08-26 16:09:19.958157:   - 未发现任何RFID标签
2025-08-26 16:09:19.958157: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:19.958157: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:20.456026: 🔄 开始RFID轮询检查...
2025-08-26 16:09:20.456026: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:20.456026: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:20.457028: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:20.457028:   - 设备句柄: 1673073161888
2025-08-26 16:09:20.457028:   - FetchRecords返回值: 0
2025-08-26 16:09:20.457028:   - 报告数量: 0
2025-08-26 16:09:20.457028: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:20.458019:   - 发现标签数量: 0
2025-08-26 16:09:20.458019:   - 未发现任何RFID标签
2025-08-26 16:09:20.458019: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:20.458019: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:20.955370: 🔄 开始RFID轮询检查...
2025-08-26 16:09:20.955370: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:20.955370: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:20.956367: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:20.956367:   - 设备句柄: 1673073161888
2025-08-26 16:09:20.956367:   - FetchRecords返回值: 0
2025-08-26 16:09:20.956367:   - 报告数量: 0
2025-08-26 16:09:20.957368: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:20.957368:   - 发现标签数量: 0
2025-08-26 16:09:20.957368:   - 未发现任何RFID标签
2025-08-26 16:09:20.957368: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:20.957368: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:21.455711: 🔄 开始RFID轮询检查...
2025-08-26 16:09:21.455711: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:21.455711: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:21.456708: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:21.456708:   - 设备句柄: 1673073161888
2025-08-26 16:09:21.456708:   - FetchRecords返回值: 0
2025-08-26 16:09:21.456708:   - 报告数量: 0
2025-08-26 16:09:21.457705: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:21.457705:   - 发现标签数量: 0
2025-08-26 16:09:21.457705:   - 未发现任何RFID标签
2025-08-26 16:09:21.457705: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:21.457705: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:21.736779: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:21.737776: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:21.737776: 🔍 数据长度: 8 字节
2025-08-26 16:09:21.737776: 🔍 预定义命令列表:
2025-08-26 16:09:21.737776:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:09:21.737776:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:09:21.737776:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:21.737776:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:21.737776:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:09:21.738772:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:09:21.738772:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:09:21.738772:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:09:21.738772:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:09:21.738772:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:09:21.738772: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-26 16:09:21.738772: 解析到闸机命令: exit_start (出馆开始)
2025-08-26 16:09:21.738772: 收到闸机命令: exit_start (出馆开始)
2025-08-26 16:09:21.739769: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-26 16:09:21.739769: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-26 16:09:21.739769: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-26 16:09:21.739769: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-26 16:09:21.739769: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:09:21.740766: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:09:21.740766: 闸机状态变更: GateState.exitStarted
2025-08-26 16:09:21.740766: 🎨 处理状态变更UI: exitStarted
2025-08-26 16:09:21.740766: 未处理的状态变更UI: exitStarted
2025-08-26 16:09:21.740766: [channel_1] 收到闸机事件: exit_start
2025-08-26 16:09:21.741763: [channel_1] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-26 16:09:21.741763: 扫描结果已清空
2025-08-26 16:09:21.741763: 🧹 [channel_1] 已清空RFID服务扫描结果（页面计数重置）
2025-08-26 16:09:21.741763: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 16:09:21.741763: 🧹 开始清空共享扫描池...
2025-08-26 16:09:21.741763: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 16:09:21.741763: 🔄 重置RFID去重集合...
2025-08-26 16:09:21.741763: 🔄 开始重置已处理条码集合...
2025-08-26 16:09:21.741763: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:09:21.742759: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:09:21.742759: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:09:21.742759: 📊 当前tagList状态: 0个标签
2025-08-26 16:09:21.742759: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:09:21.742759: 🔄 开始RFID轮询检查...
2025-08-26 16:09:21.742759: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:21.742759: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:21.742759: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 16:09:21.743756: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-26 16:09:21.743756: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-26 16:09:21.743756: 开始RFID数据收集...
2025-08-26 16:09:21.743756: 🔄 RFID数据收集已在进行中，重置防重复机制
2025-08-26 16:09:21.743756: ✅ 已处理条码列表已清空，轮询将重新发现标签
2025-08-26 16:09:21.743756: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 16:09:21.743756: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 16:09:21.744753: 清空RFID扫描缓冲区...
2025-08-26 16:09:21.744753: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:09:21.744753: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:09:21.744753: 🔄 开始重置已处理条码集合...
2025-08-26 16:09:21.744753: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:09:21.744753: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:09:21.744753: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:09:21.744753: 📊 当前tagList状态: 0个标签
2025-08-26 16:09:21.744753: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:09:21.745749: 🔄 开始RFID轮询检查...
2025-08-26 16:09:21.745749: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:21.745749: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:21.745749: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:09:21.745749: 📨 收到GateCoordinator事件: exit_start
2025-08-26 16:09:21.745749: 页面状态变更: SilencePageState.waitingExit
2025-08-26 16:09:21.745749: RFID数据收集已启动
2025-08-26 16:09:21.746746: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-26 16:09:21.746746: 🔄 开始重置已处理条码集合...
2025-08-26 16:09:21.746746: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:09:21.746746: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:09:21.746746: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:09:21.746746: 📊 当前tagList状态: 0个标签
2025-08-26 16:09:21.746746: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:09:21.746746: 🔄 开始RFID轮询检查...
2025-08-26 16:09:21.746746: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:21.746746: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:21.747742: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 16:09:21.747742: 🧹 [channel_1] 主机清空列表1和RFID缓冲区: 清除0个条码
2025-08-26 16:09:21.956052: 🔄 开始RFID轮询检查...
2025-08-26 16:09:21.956052: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:21.956052: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:21.957049: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:21.957049:   - 设备句柄: 1673073161888
2025-08-26 16:09:21.957049:   - FetchRecords返回值: 0
2025-08-26 16:09:21.957049:   - 报告数量: 0
2025-08-26 16:09:21.958045: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:21.958045:   - 发现标签数量: 0
2025-08-26 16:09:21.958045:   - 未发现任何RFID标签
2025-08-26 16:09:21.958045: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:21.958045: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:22.455396: 🔄 开始RFID轮询检查...
2025-08-26 16:09:22.455396: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:22.455396: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:22.456393: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:22.456393:   - 设备句柄: 1673073161888
2025-08-26 16:09:22.456393:   - FetchRecords返回值: 0
2025-08-26 16:09:22.456393:   - 报告数量: 0
2025-08-26 16:09:22.456393: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:22.457389:   - 发现标签数量: 0
2025-08-26 16:09:22.457389:   - 未发现任何RFID标签
2025-08-26 16:09:22.457389: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:22.457389: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:22.748424: 📊 [channel_1] 主机返回当前数据: 0个条码
2025-08-26 16:09:22.955738: 🔄 开始RFID轮询检查...
2025-08-26 16:09:22.955738: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:22.955738: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:22.956734: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:22.956734:   - 设备句柄: 1673073161888
2025-08-26 16:09:22.956734:   - FetchRecords返回值: 0
2025-08-26 16:09:22.957732:   - 报告数量: 0
2025-08-26 16:09:22.957732: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:22.957732:   - 发现标签数量: 0
2025-08-26 16:09:22.957732:   - 未发现任何RFID标签
2025-08-26 16:09:22.957732: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:22.957732: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:23.455628: 🔄 开始RFID轮询检查...
2025-08-26 16:09:23.455628: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:23.455628: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:23.456626: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:23.457623:   - 设备句柄: 1673073161888
2025-08-26 16:09:23.457623:   - FetchRecords返回值: 0
2025-08-26 16:09:23.458620:   - 报告数量: 0
2025-08-26 16:09:23.458620: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:23.458620:   - 发现标签数量: 0
2025-08-26 16:09:23.458620:   - 未发现任何RFID标签
2025-08-26 16:09:23.459617: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:23.459617: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:23.637028: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 16:09:23.638025: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 16:09:23.638025: 🔍 数据长度: 8 字节
2025-08-26 16:09:23.638025: 🔍 预定义命令列表:
2025-08-26 16:09:23.638025:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:09:23.638025:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:09:23.639023:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:23.639023:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:23.639023:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:09:23.639023:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:09:23.639023:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:09:23.639023:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:09:23.639023:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:09:23.639023:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:09:23.640017: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-26 16:09:23.640017: 解析到闸机命令: position_reached (到达指定位置)
2025-08-26 16:09:23.641014: 收到闸机命令: position_reached (到达指定位置)
2025-08-26 16:09:23.641014: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-26 16:09:23.641014: 📊 流程状态：进馆=false, 出馆=true
2025-08-26 16:09:23.641014: 📊 待处理认证：进馆=false, 出馆=false
2025-08-26 16:09:23.642019: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-26 16:09:23.642019: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 16:09:23.642019: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 16:09:23.642019: 🔐 启动出馆认证系统（不关注结果）...
2025-08-26 16:09:23.642019: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 16:09:23.642019: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 16:09:23.642019: 多认证管理器状态变更: listening
2025-08-26 16:09:23.642019: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-26 16:09:23.643009: 准备启动 1 个物理认证服务
2025-08-26 16:09:23.643009: 开始读卡器认证监听
2025-08-26 16:09:23.643009: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-26 16:09:23.643009: 已移除读卡器状态监听器
2025-08-26 16:09:23.643009: 已移除标签数据监听器
2025-08-26 16:09:23.643009: 所有卡片监听器已移除
2025-08-26 16:09:23.643009: 已添加读卡器状态监听器
2025-08-26 16:09:23.643009: 已添加标签数据监听器
2025-08-26 16:09:23.644009: 开始监听卡片数据 - 所有监听器已就绪
2025-08-26 16:09:23.644009: 读卡器认证监听启动成功
2025-08-26 16:09:23.645002: ✅ 出馆认证系统已启动
2025-08-26 16:09:23.645002: 🚀 启动出馆10秒数据收集...
2025-08-26 16:09:23.646001: 🔧 启动10秒计时器，当前时间: 2025-08-26 16:09:23.640017
2025-08-26 16:09:23.646001: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-26 16:09:23.646001: 📡 开始从共享池收集数据...
2025-08-26 16:09:23.646001: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-26 16:09:23.646001: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 16:09:23.646001: 🧹 开始清空共享扫描池...
2025-08-26 16:09:23.646994: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 16:09:23.646994: 🔄 重置RFID去重集合...
2025-08-26 16:09:23.646994: 🔄 开始重置已处理条码集合...
2025-08-26 16:09:23.646994: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:09:23.646994: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:09:23.646994: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:09:23.646994: 📊 当前tagList状态: 0个标签
2025-08-26 16:09:23.646994: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:09:23.646994: 🔄 开始RFID轮询检查...
2025-08-26 16:09:23.647991: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:23.647991: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:23.647991: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 16:09:23.647991: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 16:09:23.647991: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 16:09:23.647991: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 16:09:23.647991: 清空RFID扫描缓冲区...
2025-08-26 16:09:23.647991: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 16:09:23.647991: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 16:09:23.647991: 🔄 开始重置已处理条码集合...
2025-08-26 16:09:23.648987: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:09:23.648987: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:09:23.648987: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:09:23.648987: 📊 当前tagList状态: 0个标签
2025-08-26 16:09:23.648987: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:09:23.648987: 🔄 开始RFID轮询检查...
2025-08-26 16:09:23.648987: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:23.648987: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:23.648987: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 16:09:23.649984: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:09:23.649984: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:09:23.649984: 闸机状态变更: GateState.exitWaitingAuth
2025-08-26 16:09:23.649984: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-26 16:09:23.649984: 未处理的状态变更UI: exitWaitingAuth
2025-08-26 16:09:23.649984: 读者证 认证服务启动成功
2025-08-26 16:09:23.649984: 所有认证服务启动完成，成功启动 1 个服务
2025-08-26 16:09:23.650981: 当前可用的认证方式: 读者证
2025-08-26 16:09:23.650981: 🔄 开始重置已处理条码集合...
2025-08-26 16:09:23.650981: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 16:09:23.650981: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 16:09:23.650981: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 16:09:23.650981: 📊 当前tagList状态: 0个标签
2025-08-26 16:09:23.650981: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 16:09:23.650981: 🔄 开始RFID轮询检查...
2025-08-26 16:09:23.650981: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:23.650981: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:23.650981: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 16:09:23.651977: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:09:23.651977: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:09:23.651977: 闸机状态变更: GateState.exitScanning
2025-08-26 16:09:23.651977: 🎨 处理状态变更UI: exitScanning
2025-08-26 16:09:23.651977: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 16:09:23.651977: 🧪 模拟模式：检测到测试卡片（调用次数: 1）
2025-08-26 16:09:23.652974: 🧪 模拟模式 - 使用测试patron: 2017621493 (索引: 0, 总调用次数: 1)
2025-08-26 16:09:23.652974: 🧪 模拟检测到卡片: 2017621493
2025-08-26 16:09:23.652974: 读卡器数据认证：
2025-08-26 16:09:23.652974:   设备类型: 10
2025-08-26 16:09:23.652974:   条码: 2017621493
2025-08-26 16:09:23.652974:   标签UID: SIM2017621493
2025-08-26 16:09:23.652974:   对应登录类型: AuthLoginType.readerCard
2025-08-26 16:09:23.652974:   根据读卡器类型10确定认证方式为: 读者证
2025-08-26 16:09:23.652974:   开始调用认证API: 2017621493
2025-08-26 16:09:23.653971: 正在认证用户: 2017621493, 方式: 读者证
2025-08-26 16:09:23.653971: 多认证管理器: 读者证获得认证请求锁
2025-08-26 16:09:23.653971: 使用门径认证接口: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 16:09:23.653971: 开始门径认证: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 16:09:23.653971: 发送认证请求: {deviceMac: FFFFFFFF, patronSn: 2017621493, cardSn: null, type: 2}
2025-08-26 16:09:23.653971: 设备API服务初始化完成: http://166.111.120.166:9000
2025-08-26 16:09:23.653971: 发送读者认证请求: /tunano/ldc/entrance/v1/api/door/verify
2025-08-26 16:09:23.653971: 请求数据: {"deviceMac":"FFFFFFFF","patronSn":"2017621493","cardSn":null,"type":2}
2025-08-26 16:09:23.672908: 读者认证响应: {errorCode: 0, message: 验证通过, data: null}
2025-08-26 16:09:23.672908: 认证响应: errorCode=0, message=验证通过
2025-08-26 16:09:23.673905: 认证成功，跳过用户信息获取
2025-08-26 16:09:23.673905: 门径认证结果: AuthStatus.success, 用户=认证用户
2025-08-26 16:09:23.673905:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-26 16:09:23.673905: 认证结果已产生，立即停止读卡器扫描
2025-08-26 16:09:23.673905: 没有活跃的读卡器连接需要停止
2025-08-26 16:09:23.673905: 多认证管理器: 收到认证结果，立即停止所有读卡器扫描
2025-08-26 16:09:23.673905: 立即停止所有读卡器扫描
2025-08-26 16:09:23.674902: 停止读卡器认证监听
2025-08-26 16:09:23.674902: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-26 16:09:23.674902: 多认证管理器状态变更: authenticating
2025-08-26 16:09:23.674902: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-26 16:09:23.674902: 多认证管理器状态变更: completed
2025-08-26 16:09:23.674902: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-26 16:09:23.674902: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-26 16:09:23.674902: 已移除读卡器状态监听器
2025-08-26 16:09:23.674902: 已移除标签数据监听器
2025-08-26 16:09:23.675898: 所有卡片监听器已移除
2025-08-26 16:09:23.675898: 没有活跃的读卡器连接需要暂停
2025-08-26 16:09:23.675898: 📱 收到出馆认证结果: AuthStatus.success, 用户: 认证用户
2025-08-26 16:09:23.675898: ✅ 出馆认证API请求已发送，继续数据收集流程（不关注认证结果）
2025-08-26 16:09:23.675898: 读卡器认证监听已停止（连接保持）
2025-08-26 16:09:23.675898: 已停止 读者证 扫描
2025-08-26 16:09:23.675898: [channel_1] 收到闸机事件: exit_auth_success
2025-08-26 16:09:23.675898: 📨 收到GateCoordinator事件: exit_auth_success
2025-08-26 16:09:23.675898: 未处理的GateCoordinator事件: exit_auth_success
2025-08-26 16:09:23.955969: 🔄 开始RFID轮询检查...
2025-08-26 16:09:23.955969: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:23.955969: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:23.956967: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:23.956967:   - 设备句柄: 1673073161888
2025-08-26 16:09:23.956967:   - FetchRecords返回值: 0
2025-08-26 16:09:23.956967:   - 报告数量: 0
2025-08-26 16:09:23.956967: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:23.957963:   - 发现标签数量: 0
2025-08-26 16:09:23.957963:   - 未发现任何RFID标签
2025-08-26 16:09:23.957963: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 16:09:23.957963: 🚫 LSGate未检测到任何RFID标签
2025-08-26 16:09:24.455314: 🔄 开始RFID轮询检查...
2025-08-26 16:09:24.455314: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 16:09:24.455314: ⚠️ tagList为空，场上无标签
2025-08-26 16:09:24.457307: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:24.457307:   - 设备句柄: 1673073161888
2025-08-26 16:09:24.457307:   - FetchRecords返回值: 0
2025-08-26 16:09:24.458304:   - 报告数量: 2
2025-08-26 16:09:24.458304:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:24.458304:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:24.458304:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:24.458304:   - 设备类型: LSGControlCenter
2025-08-26 16:09:24.458304:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:24.458304:   - 数据长度: 8
2025-08-26 16:09:24.459300:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:24.459300:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:24.459300:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:24.459300:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:24.459300:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:24.459300:   - 设备类型: LSGControlCenter
2025-08-26 16:09:24.459300:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:24.460297:   - 数据长度: 8
2025-08-26 16:09:24.460297:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:24.460297:   - 提取的UID: E004015305F68508
2025-08-26 16:09:24.460297: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:24.460297:   - 发现标签数量: 2
2025-08-26 16:09:24.461297:   - 标签详情:
2025-08-26 16:09:24.461297:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:24.462296:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:24.462296: RFID扫描: 发现 2 个标签
2025-08-26 16:09:24.462296: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 16:09:24.463289: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:24.463289: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:24.463289:   - UID: E004015304F3DD22
2025-08-26 16:09:24.463289:   - Data: E004015304F3DD22
2025-08-26 16:09:24.463289:   - EventType: 1
2025-08-26 16:09:24.464285:   - Direction: 0
2025-08-26 16:09:24.464285:   - Antenna: 1
2025-08-26 16:09:24.464285:   - TagFrequency: 0
2025-08-26 16:09:24.464285: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:24.464285:   - UID: E004015305F68508
2025-08-26 16:09:24.464285:   - Data: E004015305F68508
2025-08-26 16:09:24.464285:   - EventType: 1
2025-08-26 16:09:24.465280:   - Direction: 0
2025-08-26 16:09:24.465280:   - Antenna: 1
2025-08-26 16:09:24.465280:   - TagFrequency: 0
2025-08-26 16:09:24.465280: data:E004015304F3DD22,coder:Coder15962Std
2025-08-26 16:09:24.465280: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 16:09:24.465280: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 16:09:24.465280: 🏷️ 新标签[0]: uid=E004015304F3DD22, barCode=null, readerType=22
2025-08-26 16:09:24.466277: 📡 LSGate标签详情: {uid: E004015304F3DD22, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015304F3DD22, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 16:09:24.466277: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-26 16:09:24.466277: data:E004015305F68508,coder:Coder15962Std
2025-08-26 16:09:24.466277: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 16:09:24.466277: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 16:09:24.466277: 🏷️ 新标签[0]: uid=E004015305F68508, barCode=null, readerType=22
2025-08-26 16:09:24.466277: 📡 LSGate标签详情: {uid: E004015305F68508, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015305F68508, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 16:09:24.466277: 🎯 HWTagProvider: 当前总标签数 2, 新增标签数 1
2025-08-26 16:09:24.956652: 🔄 开始RFID轮询检查...
2025-08-26 16:09:24.957649: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=0个
2025-08-26 16:09:24.957649: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:24.957649: 🆕 轮询发现新标签(UID作为条码): E004015304F3DD22
2025-08-26 16:09:24.957649: 📋 添加到已处理列表: 0 -> 1
2025-08-26 16:09:24.957649: 🏷️ 检测到标签: E004015304F3DD22
2025-08-26 16:09:24.958645: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 16:09:24.958645: 📋 已扫描列表: [E004015304F3DD22]
2025-08-26 16:09:24.958645: ✅ 条码已发送到barcodeStream，将进入共享池: E004015304F3DD22
2025-08-26 16:09:24.958645: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 16:09:24.958645: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:24.959641: 🆕 轮询发现新标签(UID作为条码): E004015305F68508
2025-08-26 16:09:24.959641: 📋 添加到已处理列表: 1 -> 2
2025-08-26 16:09:24.959641: 🏷️ 检测到标签: E004015305F68508
2025-08-26 16:09:24.959641: 📊 当前扫描状态: 扫描中=true, 已扫描=2个
2025-08-26 16:09:24.959641: 📋 已扫描列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:09:24.959641: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F68508
2025-08-26 16:09:24.959641: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 16:09:24.959641: ✅ 轮询完成: 发现2个新标签，总计已处理2个标签
2025-08-26 16:09:24.960638: 📋 当前已处理标签列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:09:24.960638: 🔄 尝试添加条码到共享池: E004015304F3DD22
2025-08-26 16:09:24.960638: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 16:09:24.960638: ✅ 成功添加条码到共享池: E004015304F3DD22 (总计: 1)
2025-08-26 16:09:24.960638: 📋 当前共享池内容: [E004015304F3DD22]
2025-08-26 16:09:24.960638: 📡 共享池变化通知已发送
2025-08-26 16:09:24.961635: 🔄 尝试添加条码到共享池: E004015304F3DD22
2025-08-26 16:09:24.961635: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 16:09:24.961635: 🔄 条码已存在于共享池: E004015304F3DD22 (总计: 1)
2025-08-26 16:09:24.961635: 扫描到新条码: E004015304F3DD22 (总计: 1)
2025-08-26 16:09:24.961635: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 16:09:24.961635: 🔄 尝试添加条码到共享池: E004015305F68508
2025-08-26 16:09:24.961635: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 16:09:24.961635: ✅ 成功添加条码到共享池: E004015305F68508 (总计: 2)
2025-08-26 16:09:24.962631: 📋 当前共享池内容: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:09:24.962631: 📡 共享池变化通知已发送
2025-08-26 16:09:24.962631: 🔄 尝试添加条码到共享池: E004015305F68508
2025-08-26 16:09:24.962631: 📊 添加前状态: 共享池大小=2, 是否为空=false
2025-08-26 16:09:24.962631: 🔄 条码已存在于共享池: E004015305F68508 (总计: 2)
2025-08-26 16:09:24.962631: 扫描到新条码: E004015305F68508 (总计: 2)
2025-08-26 16:09:24.962631: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 16:09:24.962631: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:24.963628:   - 设备句柄: 1673073161888
2025-08-26 16:09:24.963628:   - FetchRecords返回值: 0
2025-08-26 16:09:24.963628:   - 报告数量: 2
2025-08-26 16:09:24.963628:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:24.963628:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:24.963628:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:24.963628:   - 设备类型: LSGControlCenter
2025-08-26 16:09:24.963628:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:24.964625:   - 数据长度: 8
2025-08-26 16:09:24.964625:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:24.964625:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:24.964625:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:24.964625:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:24.964625:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:24.964625:   - 设备类型: LSGControlCenter
2025-08-26 16:09:24.964625:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:24.965621:   - 数据长度: 8
2025-08-26 16:09:24.965621:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:24.965621:   - 提取的UID: E004015305F68508
2025-08-26 16:09:24.965621: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:24.965621:   - 发现标签数量: 2
2025-08-26 16:09:24.965621:   - 标签详情:
2025-08-26 16:09:24.965621:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:24.965621:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:24.966618: RFID扫描: 发现 2 个标签
2025-08-26 16:09:24.966618: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 16:09:24.966618: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:24.966618: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:24.966618:   - UID: E004015304F3DD22
2025-08-26 16:09:24.966618:   - Data: E004015304F3DD22
2025-08-26 16:09:24.966618:   - EventType: 1
2025-08-26 16:09:24.966618:   - Direction: 0
2025-08-26 16:09:24.967615:   - Antenna: 1
2025-08-26 16:09:24.967615:   - TagFrequency: 0
2025-08-26 16:09:24.967615: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:24.967615:   - UID: E004015305F68508
2025-08-26 16:09:24.967615:   - Data: E004015305F68508
2025-08-26 16:09:24.967615:   - EventType: 1
2025-08-26 16:09:24.967615:   - Direction: 0
2025-08-26 16:09:24.967615:   - Antenna: 1
2025-08-26 16:09:24.968611:   - TagFrequency: 0
2025-08-26 16:09:25.455053: 🔄 开始RFID轮询检查...
2025-08-26 16:09:25.455053: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:25.455053: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:25.455053: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:25.455053: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:25.456050: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:25.456050: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:25.457047: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:25.458043:   - 设备句柄: 1673073161888
2025-08-26 16:09:25.458043:   - FetchRecords返回值: 0
2025-08-26 16:09:25.458043:   - 报告数量: 3
2025-08-26 16:09:25.459041:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:25.459041:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:25.459041:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:25.459041:   - 设备类型: LSGControlCenter
2025-08-26 16:09:25.459041:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:25.460037:   - 数据长度: 8
2025-08-26 16:09:25.460037:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:25.460037:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:25.460037:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:25.460037:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:25.460037:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:25.460037:   - 设备类型: LSGControlCenter
2025-08-26 16:09:25.461034:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:25.461034:   - 数据长度: 8
2025-08-26 16:09:25.461034:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:25.461034:   - 提取的UID: E004015305F68508
2025-08-26 16:09:25.461034:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:25.461034:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:25.461034:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:25.461034:   - 设备类型: LSGControlCenter
2025-08-26 16:09:25.462030:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:25.462030:   - 数据长度: 8
2025-08-26 16:09:25.462030:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:25.462030:   - 提取的UID: E004015305F68508
2025-08-26 16:09:25.462030: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:25.462030:   - 发现标签数量: 3
2025-08-26 16:09:25.462030:   - 标签详情:
2025-08-26 16:09:25.462030:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:25.463027:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:25.463027:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:25.463027: RFID扫描: 发现 3 个标签
2025-08-26 16:09:25.463027: 🔍 LSGate ReaderManager收到扫描结果: 3 个UID
2025-08-26 16:09:25.463027: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:25.463027: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:25.463027:   - UID: E004015304F3DD22
2025-08-26 16:09:25.463027:   - Data: E004015304F3DD22
2025-08-26 16:09:25.464023:   - EventType: 1
2025-08-26 16:09:25.464023:   - Direction: 0
2025-08-26 16:09:25.464023:   - Antenna: 1
2025-08-26 16:09:25.464023:   - TagFrequency: 0
2025-08-26 16:09:25.464023: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:25.464023:   - UID: E004015305F68508
2025-08-26 16:09:25.464023:   - Data: E004015305F68508
2025-08-26 16:09:25.465022:   - EventType: 1
2025-08-26 16:09:25.465022:   - Direction: 0
2025-08-26 16:09:25.465022:   - Antenna: 1
2025-08-26 16:09:25.466018:   - TagFrequency: 0
2025-08-26 16:09:25.466018: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:25.467018:   - UID: E004015305F68508
2025-08-26 16:09:25.467018:   - Data: E004015305F68508
2025-08-26 16:09:25.468011:   - EventType: 1
2025-08-26 16:09:25.468011:   - Direction: 0
2025-08-26 16:09:25.468011:   - Antenna: 1
2025-08-26 16:09:25.468011:   - TagFrequency: 0
2025-08-26 16:09:25.956391: 🔄 开始RFID轮询检查...
2025-08-26 16:09:25.956391: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:25.956391: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:25.956391: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:25.956391: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:25.957388: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:25.957388: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:25.959383: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:25.959383:   - 设备句柄: 1673073161888
2025-08-26 16:09:25.959383:   - FetchRecords返回值: 0
2025-08-26 16:09:25.960384:   - 报告数量: 3
2025-08-26 16:09:25.960384:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:25.960384:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:25.960384:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:25.960384:   - 设备类型: LSGControlCenter
2025-08-26 16:09:25.961380:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:25.961380:   - 数据长度: 8
2025-08-26 16:09:25.961380:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:25.961380:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:25.961380:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:25.962371:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:25.962371:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:25.962371:   - 设备类型: LSGControlCenter
2025-08-26 16:09:25.962371:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:25.962371:   - 数据长度: 8
2025-08-26 16:09:25.962371:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:25.962371:   - 提取的UID: E004015305F68508
2025-08-26 16:09:25.963368:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:25.963368:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:25.963368:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:25.963368:   - 设备类型: LSGControlCenter
2025-08-26 16:09:25.963368:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:25.963368:   - 数据长度: 8
2025-08-26 16:09:25.963368:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:25.964365:   - 提取的UID: E004015305F68508
2025-08-26 16:09:25.964365: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:25.964365:   - 发现标签数量: 3
2025-08-26 16:09:25.964365:   - 标签详情:
2025-08-26 16:09:25.964365:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:25.964365:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:25.964365:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:25.964365: RFID扫描: 发现 3 个标签
2025-08-26 16:09:25.965361: 🔍 LSGate ReaderManager收到扫描结果: 3 个UID
2025-08-26 16:09:25.965361: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:25.965361: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:25.965361:   - UID: E004015304F3DD22
2025-08-26 16:09:25.965361:   - Data: E004015304F3DD22
2025-08-26 16:09:25.965361:   - EventType: 1
2025-08-26 16:09:25.965361:   - Direction: 0
2025-08-26 16:09:25.965361:   - Antenna: 1
2025-08-26 16:09:25.966358:   - TagFrequency: 0
2025-08-26 16:09:25.966358: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:25.966358:   - UID: E004015305F68508
2025-08-26 16:09:25.966358:   - Data: E004015305F68508
2025-08-26 16:09:25.966358:   - EventType: 1
2025-08-26 16:09:25.966358:   - Direction: 0
2025-08-26 16:09:25.966358:   - Antenna: 1
2025-08-26 16:09:25.966358:   - TagFrequency: 0
2025-08-26 16:09:25.967354: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:25.967354:   - UID: E004015305F68508
2025-08-26 16:09:25.967354:   - Data: E004015305F68508
2025-08-26 16:09:25.967354:   - EventType: 1
2025-08-26 16:09:25.967354:   - Direction: 0
2025-08-26 16:09:25.967354:   - Antenna: 1
2025-08-26 16:09:25.967354:   - TagFrequency: 0
2025-08-26 16:09:26.456248: 🔄 开始RFID轮询检查...
2025-08-26 16:09:26.456248: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:26.456248: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:26.457246: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:26.457246: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:26.457246: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:26.457246: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:26.459238: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:26.459238:   - 设备句柄: 1673073161888
2025-08-26 16:09:26.459238:   - FetchRecords返回值: 0
2025-08-26 16:09:26.459238:   - 报告数量: 4
2025-08-26 16:09:26.460236:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.460236:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:26.460236:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.460236:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.460236:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.460236:   - 数据长度: 8
2025-08-26 16:09:26.460236:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:26.460236:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:26.461232:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.461232:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:26.461232:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.461232:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.461232:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.461232:   - 数据长度: 8
2025-08-26 16:09:26.461232:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:26.462229:   - 提取的UID: E004015305F68508
2025-08-26 16:09:26.462229:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.462229:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:26.462229:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.462229:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.462229:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.462229:   - 数据长度: 8
2025-08-26 16:09:26.462229:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:26.463225:   - 提取的UID: E004015305F68508
2025-08-26 16:09:26.463225:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.463225:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:26.463225:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.463225:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.463225:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.463225:   - 数据长度: 8
2025-08-26 16:09:26.464222:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:26.464222:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:26.464222: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:26.464222:   - 发现标签数量: 4
2025-08-26 16:09:26.464222:   - 标签详情:
2025-08-26 16:09:26.464222:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:26.464222:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:26.464222:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:26.465219:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:26.465219: RFID扫描: 发现 4 个标签
2025-08-26 16:09:26.465219: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 16:09:26.465219: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:26.465219: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.465219:   - UID: E004015304F3DD22
2025-08-26 16:09:26.465219:   - Data: E004015304F3DD22
2025-08-26 16:09:26.465219:   - EventType: 1
2025-08-26 16:09:26.466215:   - Direction: 0
2025-08-26 16:09:26.466215:   - Antenna: 1
2025-08-26 16:09:26.466215:   - TagFrequency: 0
2025-08-26 16:09:26.466215: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.466215:   - UID: E004015305F68508
2025-08-26 16:09:26.466215:   - Data: E004015305F68508
2025-08-26 16:09:26.466215:   - EventType: 1
2025-08-26 16:09:26.466215:   - Direction: 0
2025-08-26 16:09:26.467212:   - Antenna: 1
2025-08-26 16:09:26.467212:   - TagFrequency: 0
2025-08-26 16:09:26.467212: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.467212:   - UID: E004015305F68508
2025-08-26 16:09:26.467212:   - Data: E004015305F68508
2025-08-26 16:09:26.467212:   - EventType: 1
2025-08-26 16:09:26.467212:   - Direction: 0
2025-08-26 16:09:26.467212:   - Antenna: 1
2025-08-26 16:09:26.467212:   - TagFrequency: 0
2025-08-26 16:09:26.468209: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.468209:   - UID: E004015304F3DD22
2025-08-26 16:09:26.468209:   - Data: E004015304F3DD22
2025-08-26 16:09:26.468209:   - EventType: 1
2025-08-26 16:09:26.468209:   - Direction: 0
2025-08-26 16:09:26.468209:   - Antenna: 1
2025-08-26 16:09:26.468209:   - TagFrequency: 0
2025-08-26 16:09:26.956590: 🔄 开始RFID轮询检查...
2025-08-26 16:09:26.956590: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:26.956590: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:26.957587: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:26.957587: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:26.957587: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:26.957587: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:26.959579: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:26.960576:   - 设备句柄: 1673073161888
2025-08-26 16:09:26.960576:   - FetchRecords返回值: 0
2025-08-26 16:09:26.960576:   - 报告数量: 5
2025-08-26 16:09:26.960576:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.960576:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:26.960576:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.960576:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.961573:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.961573:   - 数据长度: 8
2025-08-26 16:09:26.961573:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:26.961573:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:26.961573:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.961573:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:26.961573:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.961573:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.962570:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.962570:   - 数据长度: 8
2025-08-26 16:09:26.962570:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:26.962570:   - 提取的UID: E004015305F68508
2025-08-26 16:09:26.962570:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.963567:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:26.963567:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.963567:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.964569:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.964569:   - 数据长度: 8
2025-08-26 16:09:26.964569:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:26.964569:   - 提取的UID: E004015305F68508
2025-08-26 16:09:26.965564:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.965564:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:26.965564:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.965564:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.966557:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.966557:   - 数据长度: 8
2025-08-26 16:09:26.966557:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:26.966557:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:26.966557:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:26.967554:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:26.967554:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:26.967554:   - 设备类型: LSGControlCenter
2025-08-26 16:09:26.967554:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:26.967554:   - 数据长度: 8
2025-08-26 16:09:26.967554:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:26.968550:   - 提取的UID: E004015305F68508
2025-08-26 16:09:26.968550: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:26.968550:   - 发现标签数量: 5
2025-08-26 16:09:26.968550:   - 标签详情:
2025-08-26 16:09:26.968550:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:26.968550:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:26.969546:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:26.969546:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:26.969546:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:26.969546: RFID扫描: 发现 5 个标签
2025-08-26 16:09:26.969546: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:26.969546: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:26.969546: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.970543:   - UID: E004015304F3DD22
2025-08-26 16:09:26.970543:   - Data: E004015304F3DD22
2025-08-26 16:09:26.970543:   - EventType: 1
2025-08-26 16:09:26.970543:   - Direction: 0
2025-08-26 16:09:26.970543:   - Antenna: 1
2025-08-26 16:09:26.970543:   - TagFrequency: 0
2025-08-26 16:09:26.970543: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.971540:   - UID: E004015305F68508
2025-08-26 16:09:26.971540:   - Data: E004015305F68508
2025-08-26 16:09:26.971540:   - EventType: 1
2025-08-26 16:09:26.971540:   - Direction: 0
2025-08-26 16:09:26.971540:   - Antenna: 1
2025-08-26 16:09:26.971540:   - TagFrequency: 0
2025-08-26 16:09:26.971540: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.972536:   - UID: E004015305F68508
2025-08-26 16:09:26.972536:   - Data: E004015305F68508
2025-08-26 16:09:26.972536:   - EventType: 1
2025-08-26 16:09:26.972536:   - Direction: 0
2025-08-26 16:09:26.972536:   - Antenna: 1
2025-08-26 16:09:26.972536:   - TagFrequency: 0
2025-08-26 16:09:26.973533: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.973533:   - UID: E004015304F3DD22
2025-08-26 16:09:26.973533:   - Data: E004015304F3DD22
2025-08-26 16:09:26.973533:   - EventType: 1
2025-08-26 16:09:26.973533:   - Direction: 0
2025-08-26 16:09:26.973533:   - Antenna: 1
2025-08-26 16:09:26.974530:   - TagFrequency: 0
2025-08-26 16:09:26.974530: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:26.974530:   - UID: E004015305F68508
2025-08-26 16:09:26.974530:   - Data: E004015305F68508
2025-08-26 16:09:26.974530:   - EventType: 1
2025-08-26 16:09:26.974530:   - Direction: 0
2025-08-26 16:09:26.974530:   - Antenna: 1
2025-08-26 16:09:26.974530:   - TagFrequency: 0
2025-08-26 16:09:27.455933: 🔄 开始RFID轮询检查...
2025-08-26 16:09:27.455933: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:27.455933: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:27.455933: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:27.456931: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:27.456931: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:27.456931: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:27.459920: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:27.459920:   - 设备句柄: 1673073161888
2025-08-26 16:09:27.459920:   - FetchRecords返回值: 0
2025-08-26 16:09:27.459920:   - 报告数量: 5
2025-08-26 16:09:27.460917:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.460917:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:27.460917:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.460917:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.460917:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.460917:   - 数据长度: 8
2025-08-26 16:09:27.460917:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:27.460917:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:27.461913:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.461913:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:27.461913:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.461913:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.461913:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.461913:   - 数据长度: 8
2025-08-26 16:09:27.461913:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:27.462910:   - 提取的UID: E004015305F68508
2025-08-26 16:09:27.462910:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.462910:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:27.462910:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.462910:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.462910:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.462910:   - 数据长度: 8
2025-08-26 16:09:27.462910:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:27.463907:   - 提取的UID: E004015305F68508
2025-08-26 16:09:27.463907:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.463907:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:27.463907:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.463907:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.463907:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.463907:   - 数据长度: 8
2025-08-26 16:09:27.463907:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:27.464903:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:27.464903:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.464903:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:27.464903:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.464903:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.464903:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.464903:   - 数据长度: 8
2025-08-26 16:09:27.465900:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:27.465900:   - 提取的UID: E004015305F68508
2025-08-26 16:09:27.465900: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:27.465900:   - 发现标签数量: 5
2025-08-26 16:09:27.465900:   - 标签详情:
2025-08-26 16:09:27.465900:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:27.465900:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:27.465900:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:27.466897:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:27.466897:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:27.466897: RFID扫描: 发现 5 个标签
2025-08-26 16:09:27.466897: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:27.466897: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:27.466897: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.466897:   - UID: E004015304F3DD22
2025-08-26 16:09:27.467893:   - Data: E004015304F3DD22
2025-08-26 16:09:27.467893:   - EventType: 1
2025-08-26 16:09:27.467893:   - Direction: 0
2025-08-26 16:09:27.467893:   - Antenna: 1
2025-08-26 16:09:27.467893:   - TagFrequency: 0
2025-08-26 16:09:27.467893: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.467893:   - UID: E004015305F68508
2025-08-26 16:09:27.467893:   - Data: E004015305F68508
2025-08-26 16:09:27.468890:   - EventType: 1
2025-08-26 16:09:27.468890:   - Direction: 0
2025-08-26 16:09:27.468890:   - Antenna: 1
2025-08-26 16:09:27.468890:   - TagFrequency: 0
2025-08-26 16:09:27.468890: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.468890:   - UID: E004015305F68508
2025-08-26 16:09:27.468890:   - Data: E004015305F68508
2025-08-26 16:09:27.468890:   - EventType: 1
2025-08-26 16:09:27.469887:   - Direction: 0
2025-08-26 16:09:27.469887:   - Antenna: 1
2025-08-26 16:09:27.469887:   - TagFrequency: 0
2025-08-26 16:09:27.469887: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.469887:   - UID: E004015304F3DD22
2025-08-26 16:09:27.469887:   - Data: E004015304F3DD22
2025-08-26 16:09:27.469887:   - EventType: 1
2025-08-26 16:09:27.469887:   - Direction: 0
2025-08-26 16:09:27.469887:   - Antenna: 1
2025-08-26 16:09:27.470884:   - TagFrequency: 0
2025-08-26 16:09:27.470884: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.470884:   - UID: E004015305F68508
2025-08-26 16:09:27.470884:   - Data: E004015305F68508
2025-08-26 16:09:27.470884:   - EventType: 1
2025-08-26 16:09:27.470884:   - Direction: 0
2025-08-26 16:09:27.470884:   - Antenna: 1
2025-08-26 16:09:27.470884:   - TagFrequency: 0
2025-08-26 16:09:27.956274: 🔄 开始RFID轮询检查...
2025-08-26 16:09:27.956274: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:27.956274: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:27.956274: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:27.956274: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:27.957272: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:27.957272: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:27.959264: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:27.960261:   - 设备句柄: 1673073161888
2025-08-26 16:09:27.960261:   - FetchRecords返回值: 0
2025-08-26 16:09:27.961258:   - 报告数量: 5
2025-08-26 16:09:27.961258:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.961258:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:27.961258:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.961258:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.962254:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.962254:   - 数据长度: 8
2025-08-26 16:09:27.962254:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:27.962254:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:27.962254:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.962254:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:27.962254:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.963251:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.963251:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.963251:   - 数据长度: 8
2025-08-26 16:09:27.963251:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:27.963251:   - 提取的UID: E004015305F68508
2025-08-26 16:09:27.963251:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.963251:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:27.963251:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.964248:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.964248:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.964248:   - 数据长度: 8
2025-08-26 16:09:27.964248:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:27.964248:   - 提取的UID: E004015305F68508
2025-08-26 16:09:27.964248:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.964248:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:27.964248:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.965244:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.965244:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.965244:   - 数据长度: 8
2025-08-26 16:09:27.965244:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:27.965244:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:27.965244:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:27.965244:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:27.965244:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:27.966241:   - 设备类型: LSGControlCenter
2025-08-26 16:09:27.966241:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:27.966241:   - 数据长度: 8
2025-08-26 16:09:27.966241:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:27.966241:   - 提取的UID: E004015305F68508
2025-08-26 16:09:27.966241: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:27.966241:   - 发现标签数量: 5
2025-08-26 16:09:27.966241:   - 标签详情:
2025-08-26 16:09:27.967238:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:27.967238:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:27.967238:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:27.968235:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:27.968235:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:27.968235: RFID扫描: 发现 5 个标签
2025-08-26 16:09:27.969232: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:27.969232: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:27.969232: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.969232:   - UID: E004015304F3DD22
2025-08-26 16:09:27.969232:   - Data: E004015304F3DD22
2025-08-26 16:09:27.970228:   - EventType: 1
2025-08-26 16:09:27.970228:   - Direction: 0
2025-08-26 16:09:27.970228:   - Antenna: 1
2025-08-26 16:09:27.970228:   - TagFrequency: 0
2025-08-26 16:09:27.970228: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.970228:   - UID: E004015305F68508
2025-08-26 16:09:27.970228:   - Data: E004015305F68508
2025-08-26 16:09:27.971224:   - EventType: 1
2025-08-26 16:09:27.971224:   - Direction: 0
2025-08-26 16:09:27.971224:   - Antenna: 1
2025-08-26 16:09:27.971224:   - TagFrequency: 0
2025-08-26 16:09:27.971224: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.971224:   - UID: E004015305F68508
2025-08-26 16:09:27.971224:   - Data: E004015305F68508
2025-08-26 16:09:27.972221:   - EventType: 1
2025-08-26 16:09:27.972221:   - Direction: 0
2025-08-26 16:09:27.972221:   - Antenna: 1
2025-08-26 16:09:27.972221:   - TagFrequency: 0
2025-08-26 16:09:27.972221: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.972221:   - UID: E004015304F3DD22
2025-08-26 16:09:27.972221:   - Data: E004015304F3DD22
2025-08-26 16:09:27.972221:   - EventType: 1
2025-08-26 16:09:27.973218:   - Direction: 0
2025-08-26 16:09:27.973218:   - Antenna: 1
2025-08-26 16:09:27.973218:   - TagFrequency: 0
2025-08-26 16:09:27.973218: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:27.973218:   - UID: E004015305F68508
2025-08-26 16:09:27.973218:   - Data: E004015305F68508
2025-08-26 16:09:27.973218:   - EventType: 1
2025-08-26 16:09:27.973218:   - Direction: 0
2025-08-26 16:09:27.974215:   - Antenna: 1
2025-08-26 16:09:27.974215:   - TagFrequency: 0
2025-08-26 16:09:28.455618: 🔄 开始RFID轮询检查...
2025-08-26 16:09:28.455618: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:28.455618: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:28.455618: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:28.455618: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:28.456616: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:28.456616: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:28.458609: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:28.459605:   - 设备句柄: 1673073161888
2025-08-26 16:09:28.459605:   - FetchRecords返回值: 0
2025-08-26 16:09:28.459605:   - 报告数量: 5
2025-08-26 16:09:28.459605:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.459605:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:28.459605:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.459605:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.460602:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.460602:   - 数据长度: 8
2025-08-26 16:09:28.460602:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:28.460602:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:28.461599:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.461599:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:28.462607:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.462607:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.463597:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.463597:   - 数据长度: 8
2025-08-26 16:09:28.463597:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:28.463597:   - 提取的UID: E004015305F68508
2025-08-26 16:09:28.464589:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.464589:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:28.464589:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.464589:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.464589:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.465586:   - 数据长度: 8
2025-08-26 16:09:28.465586:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:28.465586:   - 提取的UID: E004015305F68508
2025-08-26 16:09:28.465586:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.465586:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:28.465586:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.466583:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.466583:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.466583:   - 数据长度: 8
2025-08-26 16:09:28.466583:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:28.466583:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:28.466583:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.466583:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:28.467579:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.467579:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.467579:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.467579:   - 数据长度: 8
2025-08-26 16:09:28.467579:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:28.467579:   - 提取的UID: E004015305F68508
2025-08-26 16:09:28.468576: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:28.468576:   - 发现标签数量: 5
2025-08-26 16:09:28.468576:   - 标签详情:
2025-08-26 16:09:28.468576:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:28.468576:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:28.468576:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:28.469572:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:28.469572:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:28.469572: RFID扫描: 发现 5 个标签
2025-08-26 16:09:28.469572: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:28.469572: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:28.470569: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.470569:   - UID: E004015304F3DD22
2025-08-26 16:09:28.470569:   - Data: E004015304F3DD22
2025-08-26 16:09:28.470569:   - EventType: 1
2025-08-26 16:09:28.470569:   - Direction: 0
2025-08-26 16:09:28.470569:   - Antenna: 1
2025-08-26 16:09:28.470569:   - TagFrequency: 0
2025-08-26 16:09:28.470569: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.471566:   - UID: E004015305F68508
2025-08-26 16:09:28.471566:   - Data: E004015305F68508
2025-08-26 16:09:28.471566:   - EventType: 1
2025-08-26 16:09:28.471566:   - Direction: 0
2025-08-26 16:09:28.471566:   - Antenna: 1
2025-08-26 16:09:28.471566:   - TagFrequency: 0
2025-08-26 16:09:28.471566: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.472562:   - UID: E004015305F68508
2025-08-26 16:09:28.472562:   - Data: E004015305F68508
2025-08-26 16:09:28.472562:   - EventType: 1
2025-08-26 16:09:28.472562:   - Direction: 0
2025-08-26 16:09:28.472562:   - Antenna: 1
2025-08-26 16:09:28.472562:   - TagFrequency: 0
2025-08-26 16:09:28.472562: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.472562:   - UID: E004015304F3DD22
2025-08-26 16:09:28.473559:   - Data: E004015304F3DD22
2025-08-26 16:09:28.473559:   - EventType: 1
2025-08-26 16:09:28.473559:   - Direction: 0
2025-08-26 16:09:28.473559:   - Antenna: 1
2025-08-26 16:09:28.473559:   - TagFrequency: 0
2025-08-26 16:09:28.473559: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.473559:   - UID: E004015305F68508
2025-08-26 16:09:28.473559:   - Data: E004015305F68508
2025-08-26 16:09:28.474556:   - EventType: 1
2025-08-26 16:09:28.474556:   - Direction: 0
2025-08-26 16:09:28.474556:   - Antenna: 1
2025-08-26 16:09:28.474556:   - TagFrequency: 0
2025-08-26 16:09:28.675888: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-26 16:09:28.955969: 🔄 开始RFID轮询检查...
2025-08-26 16:09:28.956961: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:28.956961: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:28.956961: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:28.956961: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:28.957953: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:28.957953: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:28.959946: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:28.959946:   - 设备句柄: 1673073161888
2025-08-26 16:09:28.959946:   - FetchRecords返回值: 0
2025-08-26 16:09:28.959946:   - 报告数量: 5
2025-08-26 16:09:28.959946:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.959946:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:28.959946:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.960943:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.960943:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.960943:   - 数据长度: 8
2025-08-26 16:09:28.960943:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:28.960943:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:28.960943:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.960943:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:28.961940:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.961940:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.961940:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.961940:   - 数据长度: 8
2025-08-26 16:09:28.961940:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:28.961940:   - 提取的UID: E004015305F68508
2025-08-26 16:09:28.961940:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.962937:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:28.962937:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.962937:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.962937:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.962937:   - 数据长度: 8
2025-08-26 16:09:28.962937:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:28.962937:   - 提取的UID: E004015305F68508
2025-08-26 16:09:28.962937:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.963933:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:28.963933:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.963933:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.963933:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.963933:   - 数据长度: 8
2025-08-26 16:09:28.963933:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:28.963933:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:28.963933:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:28.964930:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:28.964930:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:28.964930:   - 设备类型: LSGControlCenter
2025-08-26 16:09:28.964930:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:28.964930:   - 数据长度: 8
2025-08-26 16:09:28.964930:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:28.964930:   - 提取的UID: E004015305F68508
2025-08-26 16:09:28.964930: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:28.965926:   - 发现标签数量: 5
2025-08-26 16:09:28.965926:   - 标签详情:
2025-08-26 16:09:28.965926:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:28.965926:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:28.965926:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:28.965926:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:28.965926:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:28.965926: RFID扫描: 发现 5 个标签
2025-08-26 16:09:28.966923: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:28.966923: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:28.966923: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.966923:   - UID: E004015304F3DD22
2025-08-26 16:09:28.966923:   - Data: E004015304F3DD22
2025-08-26 16:09:28.966923:   - EventType: 1
2025-08-26 16:09:28.966923:   - Direction: 0
2025-08-26 16:09:28.966923:   - Antenna: 1
2025-08-26 16:09:28.967920:   - TagFrequency: 0
2025-08-26 16:09:28.967920: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.967920:   - UID: E004015305F68508
2025-08-26 16:09:28.967920:   - Data: E004015305F68508
2025-08-26 16:09:28.967920:   - EventType: 1
2025-08-26 16:09:28.967920:   - Direction: 0
2025-08-26 16:09:28.967920:   - Antenna: 1
2025-08-26 16:09:28.967920:   - TagFrequency: 0
2025-08-26 16:09:28.967920: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.968916:   - UID: E004015305F68508
2025-08-26 16:09:28.968916:   - Data: E004015305F68508
2025-08-26 16:09:28.968916:   - EventType: 1
2025-08-26 16:09:28.968916:   - Direction: 0
2025-08-26 16:09:28.968916:   - Antenna: 1
2025-08-26 16:09:28.968916:   - TagFrequency: 0
2025-08-26 16:09:28.968916: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.969913:   - UID: E004015304F3DD22
2025-08-26 16:09:28.969913:   - Data: E004015304F3DD22
2025-08-26 16:09:28.969913:   - EventType: 1
2025-08-26 16:09:28.969913:   - Direction: 0
2025-08-26 16:09:28.969913:   - Antenna: 1
2025-08-26 16:09:28.969913:   - TagFrequency: 0
2025-08-26 16:09:28.969913: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:28.969913:   - UID: E004015305F68508
2025-08-26 16:09:28.970910:   - Data: E004015305F68508
2025-08-26 16:09:28.970910:   - EventType: 1
2025-08-26 16:09:28.970910:   - Direction: 0
2025-08-26 16:09:28.970910:   - Antenna: 1
2025-08-26 16:09:28.970910:   - TagFrequency: 0
2025-08-26 16:09:29.456913: 🔄 开始RFID轮询检查...
2025-08-26 16:09:29.457910: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:29.457910: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:29.457910: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:29.458908: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:29.458908: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:29.458908: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:29.460901: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:29.460901:   - 设备句柄: 1673073161888
2025-08-26 16:09:29.460901:   - FetchRecords返回值: 0
2025-08-26 16:09:29.460901:   - 报告数量: 5
2025-08-26 16:09:29.460901:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.461897:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:29.461897:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.461897:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.461897:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.461897:   - 数据长度: 8
2025-08-26 16:09:29.461897:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:29.461897:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:29.462894:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.462894:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:29.462894:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.462894:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.462894:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.462894:   - 数据长度: 8
2025-08-26 16:09:29.462894:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:29.462894:   - 提取的UID: E004015305F68508
2025-08-26 16:09:29.463890:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.463890:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:29.463890:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.463890:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.463890:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.463890:   - 数据长度: 8
2025-08-26 16:09:29.463890:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:29.464887:   - 提取的UID: E004015305F68508
2025-08-26 16:09:29.464887:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.464887:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:29.464887:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.465888:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.466887:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.466887:   - 数据长度: 8
2025-08-26 16:09:29.466887:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:29.467878:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:29.467878:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.467878:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:29.467878:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.467878:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.467878:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.468874:   - 数据长度: 8
2025-08-26 16:09:29.468874:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:29.468874:   - 提取的UID: E004015305F68508
2025-08-26 16:09:29.468874: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:29.468874:   - 发现标签数量: 5
2025-08-26 16:09:29.468874:   - 标签详情:
2025-08-26 16:09:29.468874:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:29.469870:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:29.469870:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:29.469870:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:29.469870:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:29.469870: RFID扫描: 发现 5 个标签
2025-08-26 16:09:29.469870: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:29.469870: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:29.470867: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.470867:   - UID: E004015304F3DD22
2025-08-26 16:09:29.470867:   - Data: E004015304F3DD22
2025-08-26 16:09:29.470867:   - EventType: 1
2025-08-26 16:09:29.470867:   - Direction: 0
2025-08-26 16:09:29.470867:   - Antenna: 1
2025-08-26 16:09:29.470867:   - TagFrequency: 0
2025-08-26 16:09:29.470867: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.471863:   - UID: E004015305F68508
2025-08-26 16:09:29.471863:   - Data: E004015305F68508
2025-08-26 16:09:29.471863:   - EventType: 1
2025-08-26 16:09:29.471863:   - Direction: 0
2025-08-26 16:09:29.471863:   - Antenna: 1
2025-08-26 16:09:29.471863:   - TagFrequency: 0
2025-08-26 16:09:29.471863: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.471863:   - UID: E004015305F68508
2025-08-26 16:09:29.472860:   - Data: E004015305F68508
2025-08-26 16:09:29.472860:   - EventType: 1
2025-08-26 16:09:29.472860:   - Direction: 0
2025-08-26 16:09:29.472860:   - Antenna: 1
2025-08-26 16:09:29.472860:   - TagFrequency: 0
2025-08-26 16:09:29.472860: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.472860:   - UID: E004015304F3DD22
2025-08-26 16:09:29.473857:   - Data: E004015304F3DD22
2025-08-26 16:09:29.473857:   - EventType: 1
2025-08-26 16:09:29.473857:   - Direction: 0
2025-08-26 16:09:29.473857:   - Antenna: 1
2025-08-26 16:09:29.473857:   - TagFrequency: 0
2025-08-26 16:09:29.473857: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.473857:   - UID: E004015305F68508
2025-08-26 16:09:29.473857:   - Data: E004015305F68508
2025-08-26 16:09:29.474854:   - EventType: 1
2025-08-26 16:09:29.474854:   - Direction: 0
2025-08-26 16:09:29.474854:   - Antenna: 1
2025-08-26 16:09:29.474854:   - TagFrequency: 0
2025-08-26 16:09:29.956257: 🔄 开始RFID轮询检查...
2025-08-26 16:09:29.956257: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:29.956257: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:29.956257: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:29.956257: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:29.957255: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:29.957255: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:29.960249: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:29.962240:   - 设备句柄: 1673073161888
2025-08-26 16:09:29.963235:   - FetchRecords返回值: 0
2025-08-26 16:09:29.963235:   - 报告数量: 5
2025-08-26 16:09:29.963235:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.964231:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:29.964231:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.964231:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.964231:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.964231:   - 数据长度: 8
2025-08-26 16:09:29.964231:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:29.965228:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:29.965228:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.965228:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:29.965228:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.965228:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.966225:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.966225:   - 数据长度: 8
2025-08-26 16:09:29.966225:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:29.966225:   - 提取的UID: E004015305F68508
2025-08-26 16:09:29.966225:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.967221:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:29.967221:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.967221:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.967221:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.967221:   - 数据长度: 8
2025-08-26 16:09:29.967221:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:29.967221:   - 提取的UID: E004015305F68508
2025-08-26 16:09:29.968218:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.968218:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:29.968218:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.968218:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.968218:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.968218:   - 数据长度: 8
2025-08-26 16:09:29.968218:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:29.969214:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:29.969214:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:29.969214:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:29.969214:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:29.969214:   - 设备类型: LSGControlCenter
2025-08-26 16:09:29.969214:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:29.969214:   - 数据长度: 8
2025-08-26 16:09:29.969214:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:29.970211:   - 提取的UID: E004015305F68508
2025-08-26 16:09:29.970211: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:29.970211:   - 发现标签数量: 5
2025-08-26 16:09:29.970211:   - 标签详情:
2025-08-26 16:09:29.970211:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:29.970211:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:29.970211:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:29.971208:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:29.971208:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:29.971208: RFID扫描: 发现 5 个标签
2025-08-26 16:09:29.971208: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:29.971208: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:29.971208: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.971208:   - UID: E004015304F3DD22
2025-08-26 16:09:29.971208:   - Data: E004015304F3DD22
2025-08-26 16:09:29.972205:   - EventType: 1
2025-08-26 16:09:29.972205:   - Direction: 0
2025-08-26 16:09:29.972205:   - Antenna: 1
2025-08-26 16:09:29.972205:   - TagFrequency: 0
2025-08-26 16:09:29.972205: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.972205:   - UID: E004015305F68508
2025-08-26 16:09:29.972205:   - Data: E004015305F68508
2025-08-26 16:09:29.972205:   - EventType: 1
2025-08-26 16:09:29.973201:   - Direction: 0
2025-08-26 16:09:29.973201:   - Antenna: 1
2025-08-26 16:09:29.973201:   - TagFrequency: 0
2025-08-26 16:09:29.973201: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.973201:   - UID: E004015305F68508
2025-08-26 16:09:29.973201:   - Data: E004015305F68508
2025-08-26 16:09:29.973201:   - EventType: 1
2025-08-26 16:09:29.973201:   - Direction: 0
2025-08-26 16:09:29.974198:   - Antenna: 1
2025-08-26 16:09:29.974198:   - TagFrequency: 0
2025-08-26 16:09:29.974198: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.974198:   - UID: E004015304F3DD22
2025-08-26 16:09:29.974198:   - Data: E004015304F3DD22
2025-08-26 16:09:29.974198:   - EventType: 1
2025-08-26 16:09:29.974198:   - Direction: 0
2025-08-26 16:09:29.974198:   - Antenna: 1
2025-08-26 16:09:29.975195:   - TagFrequency: 0
2025-08-26 16:09:29.975195: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:29.975195:   - UID: E004015305F68508
2025-08-26 16:09:29.975195:   - Data: E004015305F68508
2025-08-26 16:09:29.975195:   - EventType: 1
2025-08-26 16:09:29.975195:   - Direction: 0
2025-08-26 16:09:29.975195:   - Antenna: 1
2025-08-26 16:09:29.976191:   - TagFrequency: 0
2025-08-26 16:09:30.455602: 🔄 开始RFID轮询检查...
2025-08-26 16:09:30.455602: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:30.455602: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:30.456599: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:30.456599: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:30.456599: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:30.456599: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:30.458591: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:30.459588:   - 设备句柄: 1673073161888
2025-08-26 16:09:30.459588:   - FetchRecords返回值: 0
2025-08-26 16:09:30.459588:   - 报告数量: 5
2025-08-26 16:09:30.459588:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.459588:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:30.459588:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.459588:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.459588:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.460585:   - 数据长度: 8
2025-08-26 16:09:30.460585:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:30.460585:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:30.460585:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.460585:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:30.460585:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.460585:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.461581:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.461581:   - 数据长度: 8
2025-08-26 16:09:30.461581:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:30.461581:   - 提取的UID: E004015305F68508
2025-08-26 16:09:30.461581:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.461581:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:30.461581:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.461581:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.462578:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.462578:   - 数据长度: 8
2025-08-26 16:09:30.462578:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:30.462578:   - 提取的UID: E004015305F68508
2025-08-26 16:09:30.462578:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.462578:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:30.462578:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.462578:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.463575:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.463575:   - 数据长度: 8
2025-08-26 16:09:30.463575:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:30.463575:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:30.463575:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.463575:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:30.463575:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.464572:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.464572:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.464572:   - 数据长度: 8
2025-08-26 16:09:30.464572:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:30.464572:   - 提取的UID: E004015305F68508
2025-08-26 16:09:30.464572: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:30.464572:   - 发现标签数量: 5
2025-08-26 16:09:30.464572:   - 标签详情:
2025-08-26 16:09:30.465568:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:30.465568:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:30.465568:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:30.465568:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:30.465568:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:30.465568: RFID扫描: 发现 5 个标签
2025-08-26 16:09:30.465568: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:30.465568: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:30.466565: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.466565:   - UID: E004015304F3DD22
2025-08-26 16:09:30.466565:   - Data: E004015304F3DD22
2025-08-26 16:09:30.466565:   - EventType: 1
2025-08-26 16:09:30.466565:   - Direction: 0
2025-08-26 16:09:30.466565:   - Antenna: 1
2025-08-26 16:09:30.466565:   - TagFrequency: 0
2025-08-26 16:09:30.466565: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.466565:   - UID: E004015305F68508
2025-08-26 16:09:30.467562:   - Data: E004015305F68508
2025-08-26 16:09:30.467562:   - EventType: 1
2025-08-26 16:09:30.467562:   - Direction: 0
2025-08-26 16:09:30.467562:   - Antenna: 1
2025-08-26 16:09:30.467562:   - TagFrequency: 0
2025-08-26 16:09:30.467562: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.467562:   - UID: E004015305F68508
2025-08-26 16:09:30.467562:   - Data: E004015305F68508
2025-08-26 16:09:30.468558:   - EventType: 1
2025-08-26 16:09:30.468558:   - Direction: 0
2025-08-26 16:09:30.468558:   - Antenna: 1
2025-08-26 16:09:30.468558:   - TagFrequency: 0
2025-08-26 16:09:30.468558: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.468558:   - UID: E004015304F3DD22
2025-08-26 16:09:30.468558:   - Data: E004015304F3DD22
2025-08-26 16:09:30.468558:   - EventType: 1
2025-08-26 16:09:30.469555:   - Direction: 0
2025-08-26 16:09:30.469555:   - Antenna: 1
2025-08-26 16:09:30.469555:   - TagFrequency: 0
2025-08-26 16:09:30.469555: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.469555:   - UID: E004015305F68508
2025-08-26 16:09:30.469555:   - Data: E004015305F68508
2025-08-26 16:09:30.469555:   - EventType: 1
2025-08-26 16:09:30.470552:   - Direction: 0
2025-08-26 16:09:30.470552:   - Antenna: 1
2025-08-26 16:09:30.470552:   - TagFrequency: 0
2025-08-26 16:09:30.955943: 🔄 开始RFID轮询检查...
2025-08-26 16:09:30.955943: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:30.956942: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:30.956942: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:30.956942: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:30.956942: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:30.956942: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:30.959931: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:30.959931:   - 设备句柄: 1673073161888
2025-08-26 16:09:30.959931:   - FetchRecords返回值: 0
2025-08-26 16:09:30.959931:   - 报告数量: 5
2025-08-26 16:09:30.960926:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.960926:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:30.960926:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.960926:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.960926:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.960926:   - 数据长度: 8
2025-08-26 16:09:30.960926:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:30.960926:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:30.961923:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.961923:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:30.961923:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.961923:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.961923:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.961923:   - 数据长度: 8
2025-08-26 16:09:30.961923:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:30.962919:   - 提取的UID: E004015305F68508
2025-08-26 16:09:30.962919:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.962919:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:30.962919:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.963917:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.964914:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.964914:   - 数据长度: 8
2025-08-26 16:09:30.965911:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:30.965911:   - 提取的UID: E004015305F68508
2025-08-26 16:09:30.965911:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.965911:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:30.965911:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.966907:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.966907:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.966907:   - 数据长度: 8
2025-08-26 16:09:30.966907:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:30.966907:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:30.966907:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:30.966907:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:30.967903:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:30.967903:   - 设备类型: LSGControlCenter
2025-08-26 16:09:30.967903:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:30.967903:   - 数据长度: 8
2025-08-26 16:09:30.967903:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:30.967903:   - 提取的UID: E004015305F68508
2025-08-26 16:09:30.967903: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:30.968899:   - 发现标签数量: 5
2025-08-26 16:09:30.968899:   - 标签详情:
2025-08-26 16:09:30.968899:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:30.968899:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:30.968899:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:30.968899:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:30.968899:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:30.968899: RFID扫描: 发现 5 个标签
2025-08-26 16:09:30.969896: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:30.969896: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:30.969896: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.969896:   - UID: E004015304F3DD22
2025-08-26 16:09:30.969896:   - Data: E004015304F3DD22
2025-08-26 16:09:30.969896:   - EventType: 1
2025-08-26 16:09:30.969896:   - Direction: 0
2025-08-26 16:09:30.969896:   - Antenna: 1
2025-08-26 16:09:30.970893:   - TagFrequency: 0
2025-08-26 16:09:30.970893: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.970893:   - UID: E004015305F68508
2025-08-26 16:09:30.970893:   - Data: E004015305F68508
2025-08-26 16:09:30.970893:   - EventType: 1
2025-08-26 16:09:30.970893:   - Direction: 0
2025-08-26 16:09:30.970893:   - Antenna: 1
2025-08-26 16:09:30.970893:   - TagFrequency: 0
2025-08-26 16:09:30.971890: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.971890:   - UID: E004015305F68508
2025-08-26 16:09:30.971890:   - Data: E004015305F68508
2025-08-26 16:09:30.971890:   - EventType: 1
2025-08-26 16:09:30.971890:   - Direction: 0
2025-08-26 16:09:30.971890:   - Antenna: 1
2025-08-26 16:09:30.971890:   - TagFrequency: 0
2025-08-26 16:09:30.971890: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.972886:   - UID: E004015304F3DD22
2025-08-26 16:09:30.972886:   - Data: E004015304F3DD22
2025-08-26 16:09:30.972886:   - EventType: 1
2025-08-26 16:09:30.972886:   - Direction: 0
2025-08-26 16:09:30.972886:   - Antenna: 1
2025-08-26 16:09:30.972886:   - TagFrequency: 0
2025-08-26 16:09:30.972886: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:30.972886:   - UID: E004015305F68508
2025-08-26 16:09:30.973883:   - Data: E004015305F68508
2025-08-26 16:09:30.973883:   - EventType: 1
2025-08-26 16:09:30.973883:   - Direction: 0
2025-08-26 16:09:30.973883:   - Antenna: 1
2025-08-26 16:09:30.973883:   - TagFrequency: 0
2025-08-26 16:09:31.456289: 🔄 开始RFID轮询检查...
2025-08-26 16:09:31.456289: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:31.457282: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:31.457282: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:31.457282: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:31.458279: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:31.459274: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:31.460272: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:31.461268:   - 设备句柄: 1673073161888
2025-08-26 16:09:31.461268:   - FetchRecords返回值: 0
2025-08-26 16:09:31.462264:   - 报告数量: 5
2025-08-26 16:09:31.462264:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.462264:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:31.462264:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.462264:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.463261:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.463261:   - 数据长度: 8
2025-08-26 16:09:31.463261:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:31.463261:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:31.463261:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.464257:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:31.464257:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.464257:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.464257:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.464257:   - 数据长度: 8
2025-08-26 16:09:31.464257:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:31.465254:   - 提取的UID: E004015305F68508
2025-08-26 16:09:31.465254:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.465254:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:31.465254:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.465254:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.465254:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.466251:   - 数据长度: 8
2025-08-26 16:09:31.466251:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:31.466251:   - 提取的UID: E004015305F68508
2025-08-26 16:09:31.466251:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.466251:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:31.466251:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.466251:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.466251:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.467247:   - 数据长度: 8
2025-08-26 16:09:31.467247:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:31.467247:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:31.467247:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.467247:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:31.467247:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.467247:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.467247:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.468244:   - 数据长度: 8
2025-08-26 16:09:31.468244:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:31.468244:   - 提取的UID: E004015305F68508
2025-08-26 16:09:31.468244: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:31.468244:   - 发现标签数量: 5
2025-08-26 16:09:31.468244:   - 标签详情:
2025-08-26 16:09:31.468244:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:31.469240:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:31.469240:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:31.469240:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:31.469240:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:31.469240: RFID扫描: 发现 5 个标签
2025-08-26 16:09:31.469240: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:31.469240: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:31.469240: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.470237:   - UID: E004015304F3DD22
2025-08-26 16:09:31.470237:   - Data: E004015304F3DD22
2025-08-26 16:09:31.470237:   - EventType: 1
2025-08-26 16:09:31.470237:   - Direction: 0
2025-08-26 16:09:31.470237:   - Antenna: 1
2025-08-26 16:09:31.470237:   - TagFrequency: 0
2025-08-26 16:09:31.470237: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.470237:   - UID: E004015305F68508
2025-08-26 16:09:31.471234:   - Data: E004015305F68508
2025-08-26 16:09:31.471234:   - EventType: 1
2025-08-26 16:09:31.471234:   - Direction: 0
2025-08-26 16:09:31.471234:   - Antenna: 1
2025-08-26 16:09:31.471234:   - TagFrequency: 0
2025-08-26 16:09:31.471234: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.471234:   - UID: E004015305F68508
2025-08-26 16:09:31.471234:   - Data: E004015305F68508
2025-08-26 16:09:31.472230:   - EventType: 1
2025-08-26 16:09:31.472230:   - Direction: 0
2025-08-26 16:09:31.472230:   - Antenna: 1
2025-08-26 16:09:31.472230:   - TagFrequency: 0
2025-08-26 16:09:31.472230: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.472230:   - UID: E004015304F3DD22
2025-08-26 16:09:31.472230:   - Data: E004015304F3DD22
2025-08-26 16:09:31.472230:   - EventType: 1
2025-08-26 16:09:31.472230:   - Direction: 0
2025-08-26 16:09:31.473227:   - Antenna: 1
2025-08-26 16:09:31.473227:   - TagFrequency: 0
2025-08-26 16:09:31.473227: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.473227:   - UID: E004015305F68508
2025-08-26 16:09:31.473227:   - Data: E004015305F68508
2025-08-26 16:09:31.473227:   - EventType: 1
2025-08-26 16:09:31.473227:   - Direction: 0
2025-08-26 16:09:31.474224:   - Antenna: 1
2025-08-26 16:09:31.474224:   - TagFrequency: 0
2025-08-26 16:09:31.675556: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-26 16:09:31.675556: 多认证管理器状态变更: listening
2025-08-26 16:09:31.675556: ⚠️ 人脸识别服务未初始化或不可用
2025-08-26 16:09:31.955627: 🔄 开始RFID轮询检查...
2025-08-26 16:09:31.955627: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:31.955627: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:31.955627: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:31.956625: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:31.956625: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:31.956625: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:31.959614: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:31.959614:   - 设备句柄: 1673073161888
2025-08-26 16:09:31.959614:   - FetchRecords返回值: 0
2025-08-26 16:09:31.959614:   - 报告数量: 5
2025-08-26 16:09:31.960612:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.960612:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:31.960612:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.961609:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.961609:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.961609:   - 数据长度: 8
2025-08-26 16:09:31.961609:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:31.961609:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:31.961609:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.962605:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:31.962605:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.962605:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.962605:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.962605:   - 数据长度: 8
2025-08-26 16:09:31.962605:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:31.962605:   - 提取的UID: E004015305F68508
2025-08-26 16:09:31.963601:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.963601:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:31.963601:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.963601:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.963601:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.963601:   - 数据长度: 8
2025-08-26 16:09:31.963601:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:31.963601:   - 提取的UID: E004015305F68508
2025-08-26 16:09:31.964598:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.964598:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:31.964598:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.964598:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.964598:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.964598:   - 数据长度: 8
2025-08-26 16:09:31.964598:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:31.965595:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:31.965595:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:31.965595:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:31.965595:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:31.965595:   - 设备类型: LSGControlCenter
2025-08-26 16:09:31.965595:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:31.965595:   - 数据长度: 8
2025-08-26 16:09:31.965595:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:31.966591:   - 提取的UID: E004015305F68508
2025-08-26 16:09:31.966591: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:31.966591:   - 发现标签数量: 5
2025-08-26 16:09:31.966591:   - 标签详情:
2025-08-26 16:09:31.966591:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:31.966591:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:31.966591:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:31.967599:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:31.967599:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:31.968589: RFID扫描: 发现 5 个标签
2025-08-26 16:09:31.968589: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:31.969582: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:31.969582: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.969582:   - UID: E004015304F3DD22
2025-08-26 16:09:31.969582:   - Data: E004015304F3DD22
2025-08-26 16:09:31.970579:   - EventType: 1
2025-08-26 16:09:31.970579:   - Direction: 0
2025-08-26 16:09:31.970579:   - Antenna: 1
2025-08-26 16:09:31.970579:   - TagFrequency: 0
2025-08-26 16:09:31.970579: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.970579:   - UID: E004015305F68508
2025-08-26 16:09:31.970579:   - Data: E004015305F68508
2025-08-26 16:09:31.971575:   - EventType: 1
2025-08-26 16:09:31.971575:   - Direction: 0
2025-08-26 16:09:31.971575:   - Antenna: 1
2025-08-26 16:09:31.971575:   - TagFrequency: 0
2025-08-26 16:09:31.971575: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.971575:   - UID: E004015305F68508
2025-08-26 16:09:31.971575:   - Data: E004015305F68508
2025-08-26 16:09:31.972571:   - EventType: 1
2025-08-26 16:09:31.972571:   - Direction: 0
2025-08-26 16:09:31.972571:   - Antenna: 1
2025-08-26 16:09:31.972571:   - TagFrequency: 0
2025-08-26 16:09:31.972571: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.972571:   - UID: E004015304F3DD22
2025-08-26 16:09:31.972571:   - Data: E004015304F3DD22
2025-08-26 16:09:31.973568:   - EventType: 1
2025-08-26 16:09:31.973568:   - Direction: 0
2025-08-26 16:09:31.973568:   - Antenna: 1
2025-08-26 16:09:31.973568:   - TagFrequency: 0
2025-08-26 16:09:31.973568: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:31.973568:   - UID: E004015305F68508
2025-08-26 16:09:31.974565:   - Data: E004015305F68508
2025-08-26 16:09:31.974565:   - EventType: 1
2025-08-26 16:09:31.974565:   - Direction: 0
2025-08-26 16:09:31.974565:   - Antenna: 1
2025-08-26 16:09:31.974565:   - TagFrequency: 0
2025-08-26 16:09:32.456485: 🔄 开始RFID轮询检查...
2025-08-26 16:09:32.456485: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:32.456485: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:32.456485: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:32.457482: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:32.457482: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:32.457482: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:32.459475: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:32.460472:   - 设备句柄: 1673073161888
2025-08-26 16:09:32.460472:   - FetchRecords返回值: 0
2025-08-26 16:09:32.460472:   - 报告数量: 5
2025-08-26 16:09:32.460472:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.460472:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:32.461469:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.461469:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.462466:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.463465:   - 数据长度: 8
2025-08-26 16:09:32.463465:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:32.463465:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:32.463465:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.464459:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:32.464459:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.464459:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.464459:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.464459:   - 数据长度: 8
2025-08-26 16:09:32.464459:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:32.465455:   - 提取的UID: E004015305F68508
2025-08-26 16:09:32.465455:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.465455:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:32.465455:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.465455:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.465455:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.465455:   - 数据长度: 8
2025-08-26 16:09:32.466452:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:32.466452:   - 提取的UID: E004015305F68508
2025-08-26 16:09:32.466452:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.466452:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:32.466452:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.466452:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.466452:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.466452:   - 数据长度: 8
2025-08-26 16:09:32.467449:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:32.467449:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:32.467449:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.467449:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:32.467449:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.467449:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.467449:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.467449:   - 数据长度: 8
2025-08-26 16:09:32.468445:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:32.468445:   - 提取的UID: E004015305F68508
2025-08-26 16:09:32.468445: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:32.468445:   - 发现标签数量: 5
2025-08-26 16:09:32.468445:   - 标签详情:
2025-08-26 16:09:32.468445:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:32.468445:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:32.469442:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:32.469442:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:32.469442:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:32.469442: RFID扫描: 发现 5 个标签
2025-08-26 16:09:32.469442: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:32.469442: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:32.469442: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.469442:   - UID: E004015304F3DD22
2025-08-26 16:09:32.470439:   - Data: E004015304F3DD22
2025-08-26 16:09:32.470439:   - EventType: 1
2025-08-26 16:09:32.470439:   - Direction: 0
2025-08-26 16:09:32.470439:   - Antenna: 1
2025-08-26 16:09:32.470439:   - TagFrequency: 0
2025-08-26 16:09:32.470439: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.470439:   - UID: E004015305F68508
2025-08-26 16:09:32.470439:   - Data: E004015305F68508
2025-08-26 16:09:32.471435:   - EventType: 1
2025-08-26 16:09:32.471435:   - Direction: 0
2025-08-26 16:09:32.471435:   - Antenna: 1
2025-08-26 16:09:32.471435:   - TagFrequency: 0
2025-08-26 16:09:32.471435: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.471435:   - UID: E004015305F68508
2025-08-26 16:09:32.471435:   - Data: E004015305F68508
2025-08-26 16:09:32.471435:   - EventType: 1
2025-08-26 16:09:32.472432:   - Direction: 0
2025-08-26 16:09:32.472432:   - Antenna: 1
2025-08-26 16:09:32.472432:   - TagFrequency: 0
2025-08-26 16:09:32.472432: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.472432:   - UID: E004015304F3DD22
2025-08-26 16:09:32.472432:   - Data: E004015304F3DD22
2025-08-26 16:09:32.472432:   - EventType: 1
2025-08-26 16:09:32.472432:   - Direction: 0
2025-08-26 16:09:32.473429:   - Antenna: 1
2025-08-26 16:09:32.473429:   - TagFrequency: 0
2025-08-26 16:09:32.473429: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.473429:   - UID: E004015305F68508
2025-08-26 16:09:32.473429:   - Data: E004015305F68508
2025-08-26 16:09:32.473429:   - EventType: 1
2025-08-26 16:09:32.473429:   - Direction: 0
2025-08-26 16:09:32.473429:   - Antenna: 1
2025-08-26 16:09:32.474425:   - TagFrequency: 0
2025-08-26 16:09:32.955835: 🔄 开始RFID轮询检查...
2025-08-26 16:09:32.956826: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:32.957830: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:32.957830: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:32.957830: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:32.957830: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:32.958820: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:32.959816: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:32.959816:   - 设备句柄: 1673073161888
2025-08-26 16:09:32.959816:   - FetchRecords返回值: 0
2025-08-26 16:09:32.959816:   - 报告数量: 5
2025-08-26 16:09:32.959816:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.960815:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:32.960815:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.960815:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.960815:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.960815:   - 数据长度: 8
2025-08-26 16:09:32.961809:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:32.961809:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:32.961809:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.961809:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:32.961809:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.962806:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.962806:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.962806:   - 数据长度: 8
2025-08-26 16:09:32.962806:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:32.962806:   - 提取的UID: E004015305F68508
2025-08-26 16:09:32.962806:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.962806:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:32.963802:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.963802:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.963802:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.963802:   - 数据长度: 8
2025-08-26 16:09:32.963802:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:32.963802:   - 提取的UID: E004015305F68508
2025-08-26 16:09:32.964799:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.964799:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:32.964799:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.964799:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.964799:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.964799:   - 数据长度: 8
2025-08-26 16:09:32.964799:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:32.965796:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:32.965796:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:32.965796:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:32.965796:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:32.965796:   - 设备类型: LSGControlCenter
2025-08-26 16:09:32.965796:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:32.965796:   - 数据长度: 8
2025-08-26 16:09:32.965796:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:32.966792:   - 提取的UID: E004015305F68508
2025-08-26 16:09:32.966792: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:32.966792:   - 发现标签数量: 5
2025-08-26 16:09:32.966792:   - 标签详情:
2025-08-26 16:09:32.966792:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:32.966792:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:32.966792:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:32.966792:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:32.967789:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:32.967789: RFID扫描: 发现 5 个标签
2025-08-26 16:09:32.967789: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:32.967789: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:32.967789: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.967789:   - UID: E004015304F3DD22
2025-08-26 16:09:32.967789:   - Data: E004015304F3DD22
2025-08-26 16:09:32.967789:   - EventType: 1
2025-08-26 16:09:32.968786:   - Direction: 0
2025-08-26 16:09:32.968786:   - Antenna: 1
2025-08-26 16:09:32.968786:   - TagFrequency: 0
2025-08-26 16:09:32.968786: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.968786:   - UID: E004015305F68508
2025-08-26 16:09:32.968786:   - Data: E004015305F68508
2025-08-26 16:09:32.968786:   - EventType: 1
2025-08-26 16:09:32.968786:   - Direction: 0
2025-08-26 16:09:32.969783:   - Antenna: 1
2025-08-26 16:09:32.969783:   - TagFrequency: 0
2025-08-26 16:09:32.969783: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.969783:   - UID: E004015305F68508
2025-08-26 16:09:32.969783:   - Data: E004015305F68508
2025-08-26 16:09:32.969783:   - EventType: 1
2025-08-26 16:09:32.969783:   - Direction: 0
2025-08-26 16:09:32.969783:   - Antenna: 1
2025-08-26 16:09:32.970779:   - TagFrequency: 0
2025-08-26 16:09:32.970779: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.970779:   - UID: E004015304F3DD22
2025-08-26 16:09:32.970779:   - Data: E004015304F3DD22
2025-08-26 16:09:32.970779:   - EventType: 1
2025-08-26 16:09:32.970779:   - Direction: 0
2025-08-26 16:09:32.970779:   - Antenna: 1
2025-08-26 16:09:32.970779:   - TagFrequency: 0
2025-08-26 16:09:32.971776: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:32.971776:   - UID: E004015305F68508
2025-08-26 16:09:32.971776:   - Data: E004015305F68508
2025-08-26 16:09:32.971776:   - EventType: 1
2025-08-26 16:09:32.971776:   - Direction: 0
2025-08-26 16:09:32.971776:   - Antenna: 1
2025-08-26 16:09:32.971776:   - TagFrequency: 0
2025-08-26 16:09:33.455173: 🔄 开始RFID轮询检查...
2025-08-26 16:09:33.455173: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:33.455173: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:33.455173: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:33.455173: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:33.456170: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:33.456170: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:33.459161: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:33.459161:   - 设备句柄: 1673073161888
2025-08-26 16:09:33.459161:   - FetchRecords返回值: 0
2025-08-26 16:09:33.460157:   - 报告数量: 5
2025-08-26 16:09:33.460157:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.460157:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:33.460157:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.460157:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.460157:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.461154:   - 数据长度: 8
2025-08-26 16:09:33.461154:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:33.461154:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:33.461154:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.461154:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:33.461154:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.461154:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.462150:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.462150:   - 数据长度: 8
2025-08-26 16:09:33.462150:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:33.462150:   - 提取的UID: E004015305F68508
2025-08-26 16:09:33.462150:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.462150:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:33.462150:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.463147:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.463147:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.463147:   - 数据长度: 8
2025-08-26 16:09:33.463147:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:33.463147:   - 提取的UID: E004015305F68508
2025-08-26 16:09:33.463147:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.463147:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:33.464144:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.464144:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.464144:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.464144:   - 数据长度: 8
2025-08-26 16:09:33.464144:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:33.464144:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:33.464144:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.464144:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:33.465155:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.465155:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.465155:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.465155:   - 数据长度: 8
2025-08-26 16:09:33.466141:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:33.467134:   - 提取的UID: E004015305F68508
2025-08-26 16:09:33.467134: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:33.467134:   - 发现标签数量: 5
2025-08-26 16:09:33.468131:   - 标签详情:
2025-08-26 16:09:33.468131:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:33.468131:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:33.468131:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:33.468131:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:33.468131:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:33.469127: RFID扫描: 发现 5 个标签
2025-08-26 16:09:33.469127: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:33.469127: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:33.469127: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.469127:   - UID: E004015304F3DD22
2025-08-26 16:09:33.469127:   - Data: E004015304F3DD22
2025-08-26 16:09:33.469127:   - EventType: 1
2025-08-26 16:09:33.470124:   - Direction: 0
2025-08-26 16:09:33.470124:   - Antenna: 1
2025-08-26 16:09:33.470124:   - TagFrequency: 0
2025-08-26 16:09:33.470124: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.470124:   - UID: E004015305F68508
2025-08-26 16:09:33.470124:   - Data: E004015305F68508
2025-08-26 16:09:33.470124:   - EventType: 1
2025-08-26 16:09:33.471120:   - Direction: 0
2025-08-26 16:09:33.471120:   - Antenna: 1
2025-08-26 16:09:33.471120:   - TagFrequency: 0
2025-08-26 16:09:33.471120: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.471120:   - UID: E004015305F68508
2025-08-26 16:09:33.471120:   - Data: E004015305F68508
2025-08-26 16:09:33.471120:   - EventType: 1
2025-08-26 16:09:33.471120:   - Direction: 0
2025-08-26 16:09:33.472117:   - Antenna: 1
2025-08-26 16:09:33.472117:   - TagFrequency: 0
2025-08-26 16:09:33.472117: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.472117:   - UID: E004015304F3DD22
2025-08-26 16:09:33.472117:   - Data: E004015304F3DD22
2025-08-26 16:09:33.472117:   - EventType: 1
2025-08-26 16:09:33.472117:   - Direction: 0
2025-08-26 16:09:33.473114:   - Antenna: 1
2025-08-26 16:09:33.473114:   - TagFrequency: 0
2025-08-26 16:09:33.473114: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.473114:   - UID: E004015305F68508
2025-08-26 16:09:33.473114:   - Data: E004015305F68508
2025-08-26 16:09:33.473114:   - EventType: 1
2025-08-26 16:09:33.473114:   - Direction: 0
2025-08-26 16:09:33.473114:   - Antenna: 1
2025-08-26 16:09:33.474110:   - TagFrequency: 0
2025-08-26 16:09:33.641555: ⏰ 10秒数据收集完成，开始书籍检查... 时间: 2025-08-26 16:09:33.641555
2025-08-26 16:09:33.642554: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-26 16:09:33.642554: 🔧 停止时间: 2025-08-26 16:09:33.641555
2025-08-26 16:09:33.642554: 📡 停止从共享池收集数据...
2025-08-26 16:09:33.642554: 🔧 保持RFID扫描运行，只停止从共享池收集数据
2025-08-26 16:09:33.642554: 📡 开始从RFID服务收集UID数据...
2025-08-26 16:09:33.642554: 📊 从HWTagProvider获取UID，标签数量: 2
2025-08-26 16:09:33.642554: 📡 收集到UID: E004015304F3DD22 (条码: null)
2025-08-26 16:09:33.643549: 📡 收集到UID: E004015305F68508 (条码: null)
2025-08-26 16:09:33.643549: 📡 从RFID服务收集到UID数量: 2
2025-08-26 16:09:33.643549: 📋 UID列表: E004015304F3DD22, E004015305F68508
2025-08-26 16:09:33.643549: 📊 数据收集完成，UID列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:09:33.643549: 🔍 开始三步书籍检查，UID数量: 2
2025-08-26 16:09:33.643549: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-26 16:09:33.643549: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-26 16:09:33.643549: 🚀 开始完整的三步书籍检查流程，UID数量: 2
2025-08-26 16:09:33.644545: 🔍 第一步：检查白名单，UID数量: 2
2025-08-26 16:09:33.644545: 🔍 输入UID列表: [E004015304F3DD22, E004015305F68508]
2025-08-26 16:09:33.644545: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:09:33.644545: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:09:33.644545: 闸机状态变更: GateState.exitChecking
2025-08-26 16:09:33.644545: 🎨 处理状态变更UI: exitChecking
2025-08-26 16:09:33.644545: 未处理的状态变更UI: exitChecking
2025-08-26 16:09:33.645542: 
2025-08-26 16:09:33.645542: 📤 ========== 白名单检查接口 ==========
2025-08-26 16:09:33.645542: 📤 接口名称: 查询图书是否在白名单
2025-08-26 16:09:33.645542: 📤 请求URL: http://166.111.121.100:9000/tunano/ldc/white/tag/whitelist/contain
2025-08-26 16:09:33.645542: 📤 请求方法: POST
2025-08-26 16:09:33.645542: 📤 请求头: Content-Type: application/json
2025-08-26 16:09:33.645542: 📤 请求参数: {"Taglist":[{"Tid":"E004015304F3DD22"},{"Tid":"E004015305F68508"}]}
2025-08-26 16:09:33.645542: 📤 请求参数解析:
2025-08-26 16:09:33.646539: 📤   - Taglist: 标签列表，包含2个UID
2025-08-26 16:09:33.646539: 📤     [0] Tid: E004015304F3DD22
2025-08-26 16:09:33.646539: 📤     [1] Tid: E004015305F68508
2025-08-26 16:09:33.646539: 📤 =====================================
2025-08-26 16:09:33.646539: 
2025-08-26 16:09:33.656506: 
2025-08-26 16:09:33.656506: 📥 ========== 白名单检查响应 ==========
2025-08-26 16:09:33.656506: 📥 响应状态码: 200
2025-08-26 16:09:33.656506: 📥 响应原始数据: {"errorcode":-1,"message":"白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms","result":null}
2025-08-26 16:09:33.656506: 
2025-08-26 16:09:33.656506: 📥 响应数据解析:
2025-08-26 16:09:33.657503: 📥   - errorcode: -1 (0=在白名单内, 其他=不在白名单内)
2025-08-26 16:09:33.657503: 📥   - message: 白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms
2025-08-26 16:09:33.657503: 📥   - result: null
2025-08-26 16:09:33.657503: 🔍 解析白名单错误消息: 白名单不包含标签E004015304F3DD22,E004015305F68508, 耗时0ms
2025-08-26 16:09:33.657503: 🔍 方法1提取到的UID: E004015304F3DD22
2025-08-26 16:09:33.657503: ⚠️ 白名单检查结果: 有书籍不在白名单内
2025-08-26 16:09:33.657503: ⚠️ 不在白名单的UID字符串: E004015304F3DD22
2025-08-26 16:09:33.657503: ⚠️ 返回值: E004015304F3DD22
2025-08-26 16:09:33.657503: 📥 =====================================
2025-08-26 16:09:33.657503: 
2025-08-26 16:09:33.658499: 🔍 第二步：UID转条码，UID数量: 1
2025-08-26 16:09:33.658499: 🔍 输入UID字符串: E004015304F3DD22
2025-08-26 16:09:33.658499: 🔍 解析后UID列表: [E004015304F3DD22]
2025-08-26 16:09:33.658499: 
2025-08-26 16:09:33.658499: 📤 ========== UID转条码接口 (1/1) ==========
2025-08-26 16:09:33.658499: 📤 接口名称: 根据标签UID查询条码
2025-08-26 16:09:33.658499: 📤 请求URL: http://166.111.121.100:9000/tunano/rfdc/tag/v1/rfdc/tag/booktag/LibraryCode/CN-518000-HHLIB/Uid/E004015304F3DD22
2025-08-26 16:09:33.658499: 📤 请求方法: GET
2025-08-26 16:09:33.659496: 📤 路径参数:
2025-08-26 16:09:33.659496: 📤   - libraryCode: 从URL中提取的馆代码
2025-08-26 16:09:33.659496: 📤   - Uid: E004015304F3DD22 (标签UID/TID)
2025-08-26 16:09:33.659496: 📤 =====================================
2025-08-26 16:09:33.659496: 
2025-08-26 16:09:33.668466: 
2025-08-26 16:09:33.669463: 📥 ========== UID转条码响应 (1/1) ==========
2025-08-26 16:09:33.669463: 📥 UID: E004015304F3DD22
2025-08-26 16:09:33.669463: 📥 响应状态码: 200
2025-08-26 16:09:33.669463: 📥 响应原始数据: {"id":"2025092616082563453688","requestObject":"rfdc.tag","operation":"getbooktagbyuid","errorCode":13002,"message":"未找到标签信息","result":null}
2025-08-26 16:09:33.669463: 
2025-08-26 16:09:33.669463: 📥 响应数据解析:
2025-08-26 16:09:33.670462: 📥   - id: 2025092616082563453688
2025-08-26 16:09:33.670462: 📥   - requestObject: rfdc.tag
2025-08-26 16:09:33.671466: 📥   - operation: getbooktagbyuid
2025-08-26 16:09:33.671466: 📥   - errorCode: 13002 (0=成功, 其他=失败)
2025-08-26 16:09:33.671466: 📥   - message: 未找到标签信息
2025-08-26 16:09:33.671466: ⚠️ UID转条码失败: E004015304F3DD22
2025-08-26 16:09:33.672454: ⚠️ 错误信息: 未找到标签信息
2025-08-26 16:09:33.672454: 📥 =====================================
2025-08-26 16:09:33.672454: 
2025-08-26 16:09:33.672454: ✅ 第二步完成，获得条码数量: 0
2025-08-26 16:09:33.672454: ✅ 获得的条码列表: []
2025-08-26 16:09:33.672454: 🔍 第三步：获取图书信息，输入为空，直接返回空列表
2025-08-26 16:09:33.673450: ✅ 三步检查完成: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:09:33.673450: ✅ 三步检查完成: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:09:33.673450: 📊 检查结果: 允许通过=true, 不在白名单书籍数量=0
2025-08-26 16:09:33.673450: ✅ 允许出馆: 检测到 2 本书籍，其中 1 本不在白名单内，请通过
2025-08-26 16:09:33.673450: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-26 16:09:33.673450: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-26 16:09:33.674446: 📤 准备发送闸机命令: success_signal
2025-08-26 16:09:33.674446: 📤 成功信号已发送（异步）
2025-08-26 16:09:33.674446: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-26 16:09:33.674446: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-26 16:09:33.674446: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-26 16:09:33.674446: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-26 16:09:33.675443: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 16:09:33.675443: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:09:33.675443: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:09:33.675443: 闸机状态变更: GateState.exitOver
2025-08-26 16:09:33.675443: 🎨 处理状态变更UI: exitOver
2025-08-26 16:09:33.675443: 未处理的状态变更UI: exitOver
2025-08-26 16:09:33.675443: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 16:09:33.675443: 闸机命令发送成功: success_signal
2025-08-26 16:09:33.675443: ✅ 闸机命令 success_signal 发送成功
2025-08-26 16:09:33.676440: [channel_1] 收到闸机事件: exit_allowed
2025-08-26 16:09:33.676440: 📨 收到GateCoordinator事件: exit_allowed
2025-08-26 16:09:33.676440: 页面状态变更: SilencePageState.exitAllowed
2025-08-26 16:09:33.697370: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-26 16:09:33.697370: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-26 16:09:33.698373: 🔍 数据长度: 8 字节
2025-08-26 16:09:33.698373: 🔍 预定义命令列表:
2025-08-26 16:09:33.698373:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:09:33.699365:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:09:33.699365:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:33.699365:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:33.699365:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:09:33.699365:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:09:33.700360:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:09:33.700360:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:09:33.700360:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:09:33.700360:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:09:33.700360: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-26 16:09:33.701360: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-26 16:09:33.955514: 🔄 开始RFID轮询检查...
2025-08-26 16:09:33.955514: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:33.955514: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:33.955514: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:33.955514: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:33.956511: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:33.956511: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:33.966477: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:33.967474:   - 设备句柄: 1673073161888
2025-08-26 16:09:33.967474:   - FetchRecords返回值: 0
2025-08-26 16:09:33.967474:   - 报告数量: 5
2025-08-26 16:09:33.967474:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.967474:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:33.968471:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.968471:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.968471:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.968471:   - 数据长度: 8
2025-08-26 16:09:33.968471:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:33.968471:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:33.968471:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.969468:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:33.969468:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.969468:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.969468:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.969468:   - 数据长度: 8
2025-08-26 16:09:33.969468:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:33.969468:   - 提取的UID: E004015305F68508
2025-08-26 16:09:33.969468:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.970464:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:33.970464:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.970464:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.970464:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.970464:   - 数据长度: 8
2025-08-26 16:09:33.970464:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:33.970464:   - 提取的UID: E004015305F68508
2025-08-26 16:09:33.971461:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.971461:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:33.972458:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.972458:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.972458:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.972458:   - 数据长度: 8
2025-08-26 16:09:33.972458:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:33.972458:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:33.972458:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:33.973459:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:33.973459:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:33.973459:   - 设备类型: LSGControlCenter
2025-08-26 16:09:33.973459:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:33.974452:   - 数据长度: 8
2025-08-26 16:09:33.974452:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:33.974452:   - 提取的UID: E004015305F68508
2025-08-26 16:09:33.974452: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:33.974452:   - 发现标签数量: 5
2025-08-26 16:09:33.974452:   - 标签详情:
2025-08-26 16:09:33.975448:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:33.975448:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:33.975448:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:33.975448:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:33.975448:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:33.975448: RFID扫描: 发现 5 个标签
2025-08-26 16:09:33.976445: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:33.976445: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:33.977454: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.977454:   - UID: E004015304F3DD22
2025-08-26 16:09:33.978444:   - Data: E004015304F3DD22
2025-08-26 16:09:33.978444:   - EventType: 1
2025-08-26 16:09:33.978444:   - Direction: 0
2025-08-26 16:09:33.978444:   - Antenna: 1
2025-08-26 16:09:33.978444:   - TagFrequency: 0
2025-08-26 16:09:33.979435: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.979435:   - UID: E004015305F68508
2025-08-26 16:09:33.979435:   - Data: E004015305F68508
2025-08-26 16:09:33.979435:   - EventType: 1
2025-08-26 16:09:33.980432:   - Direction: 0
2025-08-26 16:09:33.980432:   - Antenna: 1
2025-08-26 16:09:33.980432:   - TagFrequency: 0
2025-08-26 16:09:33.980432: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.980432:   - UID: E004015305F68508
2025-08-26 16:09:33.980432:   - Data: E004015305F68508
2025-08-26 16:09:33.980432:   - EventType: 1
2025-08-26 16:09:33.981428:   - Direction: 0
2025-08-26 16:09:33.981428:   - Antenna: 1
2025-08-26 16:09:33.981428:   - TagFrequency: 0
2025-08-26 16:09:33.981428: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.981428:   - UID: E004015304F3DD22
2025-08-26 16:09:33.981428:   - Data: E004015304F3DD22
2025-08-26 16:09:33.982425:   - EventType: 1
2025-08-26 16:09:33.982425:   - Direction: 0
2025-08-26 16:09:33.982425:   - Antenna: 1
2025-08-26 16:09:33.982425:   - TagFrequency: 0
2025-08-26 16:09:33.982425: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:33.982425:   - UID: E004015305F68508
2025-08-26 16:09:33.982425:   - Data: E004015305F68508
2025-08-26 16:09:33.983422:   - EventType: 1
2025-08-26 16:09:33.983422:   - Direction: 0
2025-08-26 16:09:33.983422:   - Antenna: 1
2025-08-26 16:09:33.983422:   - TagFrequency: 0
2025-08-26 16:09:34.455855: 🔄 开始RFID轮询检查...
2025-08-26 16:09:34.455855: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:34.455855: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:34.455855: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:34.455855: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:34.456863: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:34.456863: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:34.458845: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:34.459842:   - 设备句柄: 1673073161888
2025-08-26 16:09:34.459842:   - FetchRecords返回值: 0
2025-08-26 16:09:34.459842:   - 报告数量: 5
2025-08-26 16:09:34.459842:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.459842:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:34.459842:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.460839:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.460839:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.460839:   - 数据长度: 8
2025-08-26 16:09:34.460839:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:34.460839:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:34.460839:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.460839:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:34.461835:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.461835:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.461835:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.461835:   - 数据长度: 8
2025-08-26 16:09:34.461835:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:34.461835:   - 提取的UID: E004015305F68508
2025-08-26 16:09:34.461835:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.461835:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:34.462832:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.462832:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.462832:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.462832:   - 数据长度: 8
2025-08-26 16:09:34.462832:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:34.462832:   - 提取的UID: E004015305F68508
2025-08-26 16:09:34.462832:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.462832:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:34.463828:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.463828:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.463828:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.463828:   - 数据长度: 8
2025-08-26 16:09:34.463828:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:34.463828:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:34.463828:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.463828:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:34.464825:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.464825:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.464825:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.464825:   - 数据长度: 8
2025-08-26 16:09:34.464825:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:34.464825:   - 提取的UID: E004015305F68508
2025-08-26 16:09:34.464825: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:34.465822:   - 发现标签数量: 5
2025-08-26 16:09:34.465822:   - 标签详情:
2025-08-26 16:09:34.465822:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:34.465822:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:34.465822:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:34.465822:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:34.465822:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:34.465822: RFID扫描: 发现 5 个标签
2025-08-26 16:09:34.466819: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:34.466819: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:34.466819: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.466819:   - UID: E004015304F3DD22
2025-08-26 16:09:34.466819:   - Data: E004015304F3DD22
2025-08-26 16:09:34.466819:   - EventType: 1
2025-08-26 16:09:34.466819:   - Direction: 0
2025-08-26 16:09:34.466819:   - Antenna: 1
2025-08-26 16:09:34.467815:   - TagFrequency: 0
2025-08-26 16:09:34.467815: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.467815:   - UID: E004015305F68508
2025-08-26 16:09:34.467815:   - Data: E004015305F68508
2025-08-26 16:09:34.467815:   - EventType: 1
2025-08-26 16:09:34.467815:   - Direction: 0
2025-08-26 16:09:34.467815:   - Antenna: 1
2025-08-26 16:09:34.467815:   - TagFrequency: 0
2025-08-26 16:09:34.468812: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.468812:   - UID: E004015305F68508
2025-08-26 16:09:34.468812:   - Data: E004015305F68508
2025-08-26 16:09:34.468812:   - EventType: 1
2025-08-26 16:09:34.468812:   - Direction: 0
2025-08-26 16:09:34.468812:   - Antenna: 1
2025-08-26 16:09:34.468812:   - TagFrequency: 0
2025-08-26 16:09:34.468812: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.469809:   - UID: E004015304F3DD22
2025-08-26 16:09:34.469809:   - Data: E004015304F3DD22
2025-08-26 16:09:34.469809:   - EventType: 1
2025-08-26 16:09:34.469809:   - Direction: 0
2025-08-26 16:09:34.469809:   - Antenna: 1
2025-08-26 16:09:34.469809:   - TagFrequency: 0
2025-08-26 16:09:34.469809: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.469809:   - UID: E004015305F68508
2025-08-26 16:09:34.470805:   - Data: E004015305F68508
2025-08-26 16:09:34.470805:   - EventType: 1
2025-08-26 16:09:34.470805:   - Direction: 0
2025-08-26 16:09:34.470805:   - Antenna: 1
2025-08-26 16:09:34.470805:   - TagFrequency: 0
2025-08-26 16:09:34.955199: 🔄 开始RFID轮询检查...
2025-08-26 16:09:34.955199: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:34.955199: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:34.955199: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:34.955199: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:34.956201: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:34.956201: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:34.958189: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:34.959186:   - 设备句柄: 1673073161888
2025-08-26 16:09:34.959186:   - FetchRecords返回值: 0
2025-08-26 16:09:34.959186:   - 报告数量: 5
2025-08-26 16:09:34.959186:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.959186:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:34.959186:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.960183:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.960183:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.960183:   - 数据长度: 8
2025-08-26 16:09:34.960183:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:34.960183:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:34.960183:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.960183:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:34.960183:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.961180:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.961180:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.961180:   - 数据长度: 8
2025-08-26 16:09:34.961180:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:34.961180:   - 提取的UID: E004015305F68508
2025-08-26 16:09:34.961180:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.961180:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:34.962176:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.962176:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.962176:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.962176:   - 数据长度: 8
2025-08-26 16:09:34.962176:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:34.962176:   - 提取的UID: E004015305F68508
2025-08-26 16:09:34.962176:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.962176:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:34.963173:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.963173:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.963173:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.963173:   - 数据长度: 8
2025-08-26 16:09:34.963173:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:34.963173:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:34.963173:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:34.963173:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:34.964169:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:34.964169:   - 设备类型: LSGControlCenter
2025-08-26 16:09:34.964169:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:34.964169:   - 数据长度: 8
2025-08-26 16:09:34.964169:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:34.964169:   - 提取的UID: E004015305F68508
2025-08-26 16:09:34.964169: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:34.964169:   - 发现标签数量: 5
2025-08-26 16:09:34.965166:   - 标签详情:
2025-08-26 16:09:34.965166:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:34.965166:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:34.965166:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:34.965166:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:34.965166:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:34.965166: RFID扫描: 发现 5 个标签
2025-08-26 16:09:34.965166: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:34.966163: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:34.966163: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.966163:   - UID: E004015304F3DD22
2025-08-26 16:09:34.966163:   - Data: E004015304F3DD22
2025-08-26 16:09:34.966163:   - EventType: 1
2025-08-26 16:09:34.966163:   - Direction: 0
2025-08-26 16:09:34.966163:   - Antenna: 1
2025-08-26 16:09:34.966163:   - TagFrequency: 0
2025-08-26 16:09:34.967159: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.967159:   - UID: E004015305F68508
2025-08-26 16:09:34.967159:   - Data: E004015305F68508
2025-08-26 16:09:34.967159:   - EventType: 1
2025-08-26 16:09:34.967159:   - Direction: 0
2025-08-26 16:09:34.967159:   - Antenna: 1
2025-08-26 16:09:34.967159:   - TagFrequency: 0
2025-08-26 16:09:34.967159: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.968156:   - UID: E004015305F68508
2025-08-26 16:09:34.968156:   - Data: E004015305F68508
2025-08-26 16:09:34.968156:   - EventType: 1
2025-08-26 16:09:34.968156:   - Direction: 0
2025-08-26 16:09:34.968156:   - Antenna: 1
2025-08-26 16:09:34.968156:   - TagFrequency: 0
2025-08-26 16:09:34.968156: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.968156:   - UID: E004015304F3DD22
2025-08-26 16:09:34.969153:   - Data: E004015304F3DD22
2025-08-26 16:09:34.969153:   - EventType: 1
2025-08-26 16:09:34.969153:   - Direction: 0
2025-08-26 16:09:34.969153:   - Antenna: 1
2025-08-26 16:09:34.969153:   - TagFrequency: 0
2025-08-26 16:09:34.969153: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:34.969153:   - UID: E004015305F68508
2025-08-26 16:09:34.970150:   - Data: E004015305F68508
2025-08-26 16:09:34.970150:   - EventType: 1
2025-08-26 16:09:34.970150:   - Direction: 0
2025-08-26 16:09:34.970150:   - Antenna: 1
2025-08-26 16:09:34.970150:   - TagFrequency: 0
2025-08-26 16:09:35.455574: 🔄 开始RFID轮询检查...
2025-08-26 16:09:35.455574: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:35.455574: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:35.455574: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:35.456577: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:35.456577: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:35.456577: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:35.458564: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:35.459560:   - 设备句柄: 1673073161888
2025-08-26 16:09:35.459560:   - FetchRecords返回值: 0
2025-08-26 16:09:35.459560:   - 报告数量: 5
2025-08-26 16:09:35.459560:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.459560:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:35.459560:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.459560:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.460557:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.460557:   - 数据长度: 8
2025-08-26 16:09:35.460557:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:35.460557:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:35.460557:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.460557:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:35.460557:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.460557:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.461554:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.461554:   - 数据长度: 8
2025-08-26 16:09:35.461554:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:35.461554:   - 提取的UID: E004015305F68508
2025-08-26 16:09:35.461554:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.461554:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:35.461554:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.462551:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.462551:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.462551:   - 数据长度: 8
2025-08-26 16:09:35.462551:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:35.462551:   - 提取的UID: E004015305F68508
2025-08-26 16:09:35.462551:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.462551:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:35.462551:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.463547:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.463547:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.463547:   - 数据长度: 8
2025-08-26 16:09:35.463547:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:35.463547:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:35.463547:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.463547:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:35.463547:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.464544:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.464544:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.464544:   - 数据长度: 8
2025-08-26 16:09:35.464544:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:35.464544:   - 提取的UID: E004015305F68508
2025-08-26 16:09:35.464544: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:35.464544:   - 发现标签数量: 5
2025-08-26 16:09:35.465541:   - 标签详情:
2025-08-26 16:09:35.465541:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:35.465541:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:35.465541:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:35.465541:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:35.465541:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:35.465541: RFID扫描: 发现 5 个标签
2025-08-26 16:09:35.465541: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:35.466537: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:35.466537: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.466537:   - UID: E004015304F3DD22
2025-08-26 16:09:35.466537:   - Data: E004015304F3DD22
2025-08-26 16:09:35.466537:   - EventType: 1
2025-08-26 16:09:35.466537:   - Direction: 0
2025-08-26 16:09:35.466537:   - Antenna: 1
2025-08-26 16:09:35.466537:   - TagFrequency: 0
2025-08-26 16:09:35.467534: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.467534:   - UID: E004015305F68508
2025-08-26 16:09:35.467534:   - Data: E004015305F68508
2025-08-26 16:09:35.467534:   - EventType: 1
2025-08-26 16:09:35.467534:   - Direction: 0
2025-08-26 16:09:35.467534:   - Antenna: 1
2025-08-26 16:09:35.467534:   - TagFrequency: 0
2025-08-26 16:09:35.467534: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.468531:   - UID: E004015305F68508
2025-08-26 16:09:35.468531:   - Data: E004015305F68508
2025-08-26 16:09:35.468531:   - EventType: 1
2025-08-26 16:09:35.468531:   - Direction: 0
2025-08-26 16:09:35.468531:   - Antenna: 1
2025-08-26 16:09:35.468531:   - TagFrequency: 0
2025-08-26 16:09:35.468531: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.468531:   - UID: E004015304F3DD22
2025-08-26 16:09:35.469527:   - Data: E004015304F3DD22
2025-08-26 16:09:35.469527:   - EventType: 1
2025-08-26 16:09:35.469527:   - Direction: 0
2025-08-26 16:09:35.469527:   - Antenna: 1
2025-08-26 16:09:35.469527:   - TagFrequency: 0
2025-08-26 16:09:35.469527: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.469527:   - UID: E004015305F68508
2025-08-26 16:09:35.469527:   - Data: E004015305F68508
2025-08-26 16:09:35.470524:   - EventType: 1
2025-08-26 16:09:35.470524:   - Direction: 0
2025-08-26 16:09:35.470524:   - Antenna: 1
2025-08-26 16:09:35.470524:   - TagFrequency: 0
2025-08-26 16:09:35.955916: 🔄 开始RFID轮询检查...
2025-08-26 16:09:35.955916: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:35.955916: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:35.955916: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:35.955916: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:35.956918: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:35.956918: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:35.958905: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:35.959901:   - 设备句柄: 1673073161888
2025-08-26 16:09:35.959901:   - FetchRecords返回值: 0
2025-08-26 16:09:35.959901:   - 报告数量: 5
2025-08-26 16:09:35.959901:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.959901:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:35.959901:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.960898:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.960898:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.960898:   - 数据长度: 8
2025-08-26 16:09:35.960898:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:35.960898:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:35.960898:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.960898:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:35.960898:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.961895:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.961895:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.961895:   - 数据长度: 8
2025-08-26 16:09:35.961895:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:35.961895:   - 提取的UID: E004015305F68508
2025-08-26 16:09:35.961895:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.961895:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:35.962892:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.962892:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.962892:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.962892:   - 数据长度: 8
2025-08-26 16:09:35.962892:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:35.962892:   - 提取的UID: E004015305F68508
2025-08-26 16:09:35.962892:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.962892:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:35.963888:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.963888:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.963888:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.963888:   - 数据长度: 8
2025-08-26 16:09:35.963888:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:35.963888:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:35.963888:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:35.963888:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:35.964885:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:35.964885:   - 设备类型: LSGControlCenter
2025-08-26 16:09:35.964885:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:35.964885:   - 数据长度: 8
2025-08-26 16:09:35.964885:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:35.964885:   - 提取的UID: E004015305F68508
2025-08-26 16:09:35.964885: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:35.964885:   - 发现标签数量: 5
2025-08-26 16:09:35.965882:   - 标签详情:
2025-08-26 16:09:35.965882:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:35.965882:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:35.965882:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:35.965882:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:35.965882:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:35.965882: RFID扫描: 发现 5 个标签
2025-08-26 16:09:35.965882: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:35.966878: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:35.966878: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.966878:   - UID: E004015304F3DD22
2025-08-26 16:09:35.966878:   - Data: E004015304F3DD22
2025-08-26 16:09:35.966878:   - EventType: 1
2025-08-26 16:09:35.966878:   - Direction: 0
2025-08-26 16:09:35.966878:   - Antenna: 1
2025-08-26 16:09:35.967875:   - TagFrequency: 0
2025-08-26 16:09:35.967875: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.967875:   - UID: E004015305F68508
2025-08-26 16:09:35.967875:   - Data: E004015305F68508
2025-08-26 16:09:35.967875:   - EventType: 1
2025-08-26 16:09:35.967875:   - Direction: 0
2025-08-26 16:09:35.967875:   - Antenna: 1
2025-08-26 16:09:35.967875:   - TagFrequency: 0
2025-08-26 16:09:35.968872: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.968872:   - UID: E004015305F68508
2025-08-26 16:09:35.968872:   - Data: E004015305F68508
2025-08-26 16:09:35.968872:   - EventType: 1
2025-08-26 16:09:35.968872:   - Direction: 0
2025-08-26 16:09:35.968872:   - Antenna: 1
2025-08-26 16:09:35.968872:   - TagFrequency: 0
2025-08-26 16:09:35.968872: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.969868:   - UID: E004015304F3DD22
2025-08-26 16:09:35.969868:   - Data: E004015304F3DD22
2025-08-26 16:09:35.969868:   - EventType: 1
2025-08-26 16:09:35.969868:   - Direction: 0
2025-08-26 16:09:35.969868:   - Antenna: 1
2025-08-26 16:09:35.969868:   - TagFrequency: 0
2025-08-26 16:09:35.969868: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:35.969868:   - UID: E004015305F68508
2025-08-26 16:09:35.970865:   - Data: E004015305F68508
2025-08-26 16:09:35.970865:   - EventType: 1
2025-08-26 16:09:35.970865:   - Direction: 0
2025-08-26 16:09:35.970865:   - Antenna: 1
2025-08-26 16:09:35.970865:   - TagFrequency: 0
2025-08-26 16:09:36.455259: 🔄 开始RFID轮询检查...
2025-08-26 16:09:36.455259: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:36.455259: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:36.455259: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:36.456255: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:36.456255: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:36.456255: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:36.458249: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:36.459246:   - 设备句柄: 1673073161888
2025-08-26 16:09:36.459246:   - FetchRecords返回值: 0
2025-08-26 16:09:36.459246:   - 报告数量: 5
2025-08-26 16:09:36.459246:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.459246:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:36.459246:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.459246:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.460242:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.460242:   - 数据长度: 8
2025-08-26 16:09:36.460242:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:36.460242:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:36.460242:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.460242:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:36.460242:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.460242:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.461239:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.461239:   - 数据长度: 8
2025-08-26 16:09:36.461239:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:36.461239:   - 提取的UID: E004015305F68508
2025-08-26 16:09:36.461239:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.461239:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:36.461239:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.462236:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.462236:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.462236:   - 数据长度: 8
2025-08-26 16:09:36.462236:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:36.462236:   - 提取的UID: E004015305F68508
2025-08-26 16:09:36.462236:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.462236:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:36.462236:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.463232:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.463232:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.463232:   - 数据长度: 8
2025-08-26 16:09:36.463232:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:36.463232:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:36.463232:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.463232:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:36.464229:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.464229:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.464229:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.464229:   - 数据长度: 8
2025-08-26 16:09:36.464229:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:36.464229:   - 提取的UID: E004015305F68508
2025-08-26 16:09:36.464229: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:36.464229:   - 发现标签数量: 5
2025-08-26 16:09:36.464229:   - 标签详情:
2025-08-26 16:09:36.465226:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:36.465226:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:36.465226:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:36.465226:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:36.465226:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:36.465226: RFID扫描: 发现 5 个标签
2025-08-26 16:09:36.465226: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:36.466222: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:36.466222: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.466222:   - UID: E004015304F3DD22
2025-08-26 16:09:36.466222:   - Data: E004015304F3DD22
2025-08-26 16:09:36.466222:   - EventType: 1
2025-08-26 16:09:36.466222:   - Direction: 0
2025-08-26 16:09:36.466222:   - Antenna: 1
2025-08-26 16:09:36.466222:   - TagFrequency: 0
2025-08-26 16:09:36.467219: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.467219:   - UID: E004015305F68508
2025-08-26 16:09:36.467219:   - Data: E004015305F68508
2025-08-26 16:09:36.467219:   - EventType: 1
2025-08-26 16:09:36.467219:   - Direction: 0
2025-08-26 16:09:36.467219:   - Antenna: 1
2025-08-26 16:09:36.467219:   - TagFrequency: 0
2025-08-26 16:09:36.467219: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.468216:   - UID: E004015305F68508
2025-08-26 16:09:36.468216:   - Data: E004015305F68508
2025-08-26 16:09:36.468216:   - EventType: 1
2025-08-26 16:09:36.468216:   - Direction: 0
2025-08-26 16:09:36.468216:   - Antenna: 1
2025-08-26 16:09:36.468216:   - TagFrequency: 0
2025-08-26 16:09:36.468216: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.468216:   - UID: E004015304F3DD22
2025-08-26 16:09:36.469212:   - Data: E004015304F3DD22
2025-08-26 16:09:36.469212:   - EventType: 1
2025-08-26 16:09:36.469212:   - Direction: 0
2025-08-26 16:09:36.469212:   - Antenna: 1
2025-08-26 16:09:36.469212:   - TagFrequency: 0
2025-08-26 16:09:36.469212: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.469212:   - UID: E004015305F68508
2025-08-26 16:09:36.469212:   - Data: E004015305F68508
2025-08-26 16:09:36.470209:   - EventType: 1
2025-08-26 16:09:36.470209:   - Direction: 0
2025-08-26 16:09:36.470209:   - Antenna: 1
2025-08-26 16:09:36.470209:   - TagFrequency: 0
2025-08-26 16:09:36.674531: 模拟出馆完成，状态重置为idle
2025-08-26 16:09:36.674531: 闸机状态变更: GateState.exitOver -> GateState.idle
2025-08-26 16:09:36.674531: 闸机状态更新: GateState.exitOver -> GateState.idle
2025-08-26 16:09:36.674531: [channel_1] 收到闸机事件: state_changed
2025-08-26 16:09:36.675534: 📨 收到GateCoordinator事件: state_changed
2025-08-26 16:09:36.675534: 闸机状态变更: GateState.idle
2025-08-26 16:09:36.675534: 🎨 处理状态变更UI: idle
2025-08-26 16:09:36.675534: 页面状态变更: SilencePageState.welcome
2025-08-26 16:09:36.955607: 🔄 开始RFID轮询检查...
2025-08-26 16:09:36.956607: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:36.956607: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:36.956607: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:36.956607: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:36.957594: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:36.957594: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:36.959587: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:36.959587:   - 设备句柄: 1673073161888
2025-08-26 16:09:36.959587:   - FetchRecords返回值: 0
2025-08-26 16:09:36.959587:   - 报告数量: 5
2025-08-26 16:09:36.959587:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.959587:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:36.959587:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.960584:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.960584:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.960584:   - 数据长度: 8
2025-08-26 16:09:36.960584:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:36.960584:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:36.960584:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.960584:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:36.961580:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.961580:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.961580:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.961580:   - 数据长度: 8
2025-08-26 16:09:36.961580:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:36.961580:   - 提取的UID: E004015305F68508
2025-08-26 16:09:36.961580:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.961580:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:36.962577:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.962577:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.962577:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.962577:   - 数据长度: 8
2025-08-26 16:09:36.962577:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:36.962577:   - 提取的UID: E004015305F68508
2025-08-26 16:09:36.962577:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.962577:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:36.963573:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.963573:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.963573:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.963573:   - 数据长度: 8
2025-08-26 16:09:36.963573:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:36.963573:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:36.963573:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:36.964570:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:36.964570:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:36.964570:   - 设备类型: LSGControlCenter
2025-08-26 16:09:36.964570:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:36.964570:   - 数据长度: 8
2025-08-26 16:09:36.964570:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:36.964570:   - 提取的UID: E004015305F68508
2025-08-26 16:09:36.964570: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:36.965566:   - 发现标签数量: 5
2025-08-26 16:09:36.965566:   - 标签详情:
2025-08-26 16:09:36.965566:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:36.965566:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:36.965566:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:36.965566:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:36.965566:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:36.965566: RFID扫描: 发现 5 个标签
2025-08-26 16:09:36.966563: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:36.966563: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:36.966563: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.966563:   - UID: E004015304F3DD22
2025-08-26 16:09:36.966563:   - Data: E004015304F3DD22
2025-08-26 16:09:36.966563:   - EventType: 1
2025-08-26 16:09:36.966563:   - Direction: 0
2025-08-26 16:09:36.966563:   - Antenna: 1
2025-08-26 16:09:36.967560:   - TagFrequency: 0
2025-08-26 16:09:36.967560: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.967560:   - UID: E004015305F68508
2025-08-26 16:09:36.967560:   - Data: E004015305F68508
2025-08-26 16:09:36.967560:   - EventType: 1
2025-08-26 16:09:36.967560:   - Direction: 0
2025-08-26 16:09:36.967560:   - Antenna: 1
2025-08-26 16:09:36.967560:   - TagFrequency: 0
2025-08-26 16:09:36.968557: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.968557:   - UID: E004015305F68508
2025-08-26 16:09:36.968557:   - Data: E004015305F68508
2025-08-26 16:09:36.968557:   - EventType: 1
2025-08-26 16:09:36.968557:   - Direction: 0
2025-08-26 16:09:36.968557:   - Antenna: 1
2025-08-26 16:09:36.968557:   - TagFrequency: 0
2025-08-26 16:09:36.968557: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.969553:   - UID: E004015304F3DD22
2025-08-26 16:09:36.969553:   - Data: E004015304F3DD22
2025-08-26 16:09:36.969553:   - EventType: 1
2025-08-26 16:09:36.969553:   - Direction: 0
2025-08-26 16:09:36.969553:   - Antenna: 1
2025-08-26 16:09:36.969553:   - TagFrequency: 0
2025-08-26 16:09:36.969553: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:36.969553:   - UID: E004015305F68508
2025-08-26 16:09:36.970550:   - Data: E004015305F68508
2025-08-26 16:09:36.970550:   - EventType: 1
2025-08-26 16:09:36.970550:   - Direction: 0
2025-08-26 16:09:36.970550:   - Antenna: 1
2025-08-26 16:09:36.970550:   - TagFrequency: 0
2025-08-26 16:09:37.456937: 🔄 开始RFID轮询检查...
2025-08-26 16:09:37.457935: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:37.457935: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:37.458932: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:37.458932: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:37.458932: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:37.458932: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:37.460924: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:37.460924:   - 设备句柄: 1673073161888
2025-08-26 16:09:37.460924:   - FetchRecords返回值: 0
2025-08-26 16:09:37.460924:   - 报告数量: 5
2025-08-26 16:09:37.460924:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.461921:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:37.461921:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.461921:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.461921:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.461921:   - 数据长度: 8
2025-08-26 16:09:37.461921:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:37.461921:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:37.461921:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.462917:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:37.462917:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.462917:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.462917:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.462917:   - 数据长度: 8
2025-08-26 16:09:37.462917:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:37.462917:   - 提取的UID: E004015305F68508
2025-08-26 16:09:37.463914:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.463914:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:37.463914:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.463914:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.463914:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.463914:   - 数据长度: 8
2025-08-26 16:09:37.463914:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:37.463914:   - 提取的UID: E004015305F68508
2025-08-26 16:09:37.464911:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.464911:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:37.464911:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.464911:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.464911:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.464911:   - 数据长度: 8
2025-08-26 16:09:37.465913:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:37.465913:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:37.466910:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.466910:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:37.467901:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.467901:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.467901:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.467901:   - 数据长度: 8
2025-08-26 16:09:37.468898:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:37.468898:   - 提取的UID: E004015305F68508
2025-08-26 16:09:37.468898: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:37.468898:   - 发现标签数量: 5
2025-08-26 16:09:37.468898:   - 标签详情:
2025-08-26 16:09:37.468898:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:37.468898:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:37.469894:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:37.469894:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:37.469894:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:37.469894: RFID扫描: 发现 5 个标签
2025-08-26 16:09:37.469894: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:37.469894: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:37.469894: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.470892:   - UID: E004015304F3DD22
2025-08-26 16:09:37.470892:   - Data: E004015304F3DD22
2025-08-26 16:09:37.470892:   - EventType: 1
2025-08-26 16:09:37.470892:   - Direction: 0
2025-08-26 16:09:37.470892:   - Antenna: 1
2025-08-26 16:09:37.470892:   - TagFrequency: 0
2025-08-26 16:09:37.470892: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.470892:   - UID: E004015305F68508
2025-08-26 16:09:37.471888:   - Data: E004015305F68508
2025-08-26 16:09:37.471888:   - EventType: 1
2025-08-26 16:09:37.471888:   - Direction: 0
2025-08-26 16:09:37.471888:   - Antenna: 1
2025-08-26 16:09:37.471888:   - TagFrequency: 0
2025-08-26 16:09:37.471888: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.471888:   - UID: E004015305F68508
2025-08-26 16:09:37.472885:   - Data: E004015305F68508
2025-08-26 16:09:37.472885:   - EventType: 1
2025-08-26 16:09:37.472885:   - Direction: 0
2025-08-26 16:09:37.472885:   - Antenna: 1
2025-08-26 16:09:37.472885:   - TagFrequency: 0
2025-08-26 16:09:37.472885: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.472885:   - UID: E004015304F3DD22
2025-08-26 16:09:37.472885:   - Data: E004015304F3DD22
2025-08-26 16:09:37.473881:   - EventType: 1
2025-08-26 16:09:37.473881:   - Direction: 0
2025-08-26 16:09:37.473881:   - Antenna: 1
2025-08-26 16:09:37.473881:   - TagFrequency: 0
2025-08-26 16:09:37.473881: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.473881:   - UID: E004015305F68508
2025-08-26 16:09:37.473881:   - Data: E004015305F68508
2025-08-26 16:09:37.473881:   - EventType: 1
2025-08-26 16:09:37.474878:   - Direction: 0
2025-08-26 16:09:37.474878:   - Antenna: 1
2025-08-26 16:09:37.474878:   - TagFrequency: 0
2025-08-26 16:09:37.955284: 🔄 开始RFID轮询检查...
2025-08-26 16:09:37.955284: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:37.955284: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:37.955284: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:37.955284: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:37.956281: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:37.956281: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:37.958274: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:37.959274:   - 设备句柄: 1673073161888
2025-08-26 16:09:37.959274:   - FetchRecords返回值: 0
2025-08-26 16:09:37.960269:   - 报告数量: 5
2025-08-26 16:09:37.960269:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.960269:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:37.961265:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.961265:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.961265:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.961265:   - 数据长度: 8
2025-08-26 16:09:37.961265:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:37.962262:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:37.962262:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.962262:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:37.962262:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.962262:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.962262:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.963258:   - 数据长度: 8
2025-08-26 16:09:37.963258:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:37.963258:   - 提取的UID: E004015305F68508
2025-08-26 16:09:37.963258:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.963258:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:37.963258:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.963258:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.963258:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.964255:   - 数据长度: 8
2025-08-26 16:09:37.964255:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:37.964255:   - 提取的UID: E004015305F68508
2025-08-26 16:09:37.964255:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.964255:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:37.964255:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.964255:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.964255:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.965251:   - 数据长度: 8
2025-08-26 16:09:37.965251:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:37.965251:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:37.965251:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:37.965251:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:37.965251:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:37.965251:   - 设备类型: LSGControlCenter
2025-08-26 16:09:37.966248:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:37.966248:   - 数据长度: 8
2025-08-26 16:09:37.966248:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:37.966248:   - 提取的UID: E004015305F68508
2025-08-26 16:09:37.966248: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:37.966248:   - 发现标签数量: 5
2025-08-26 16:09:37.966248:   - 标签详情:
2025-08-26 16:09:37.966248:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:37.967245:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:37.967245:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:37.967245:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:37.967245:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:37.967245: RFID扫描: 发现 5 个标签
2025-08-26 16:09:37.967245: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:37.967245: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:37.967245: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.968241:   - UID: E004015304F3DD22
2025-08-26 16:09:37.968241:   - Data: E004015304F3DD22
2025-08-26 16:09:37.968241:   - EventType: 1
2025-08-26 16:09:37.968241:   - Direction: 0
2025-08-26 16:09:37.968241:   - Antenna: 1
2025-08-26 16:09:37.968241:   - TagFrequency: 0
2025-08-26 16:09:37.968241: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.969238:   - UID: E004015305F68508
2025-08-26 16:09:37.969238:   - Data: E004015305F68508
2025-08-26 16:09:37.969238:   - EventType: 1
2025-08-26 16:09:37.969238:   - Direction: 0
2025-08-26 16:09:37.969238:   - Antenna: 1
2025-08-26 16:09:37.969238:   - TagFrequency: 0
2025-08-26 16:09:37.969238: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.969238:   - UID: E004015305F68508
2025-08-26 16:09:37.970235:   - Data: E004015305F68508
2025-08-26 16:09:37.970235:   - EventType: 1
2025-08-26 16:09:37.970235:   - Direction: 0
2025-08-26 16:09:37.970235:   - Antenna: 1
2025-08-26 16:09:37.970235:   - TagFrequency: 0
2025-08-26 16:09:37.970235: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.970235:   - UID: E004015304F3DD22
2025-08-26 16:09:37.970235:   - Data: E004015304F3DD22
2025-08-26 16:09:37.971231:   - EventType: 1
2025-08-26 16:09:37.971231:   - Direction: 0
2025-08-26 16:09:37.971231:   - Antenna: 1
2025-08-26 16:09:37.971231:   - TagFrequency: 0
2025-08-26 16:09:37.971231: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:37.971231:   - UID: E004015305F68508
2025-08-26 16:09:37.971231:   - Data: E004015305F68508
2025-08-26 16:09:37.971231:   - EventType: 1
2025-08-26 16:09:37.972228:   - Direction: 0
2025-08-26 16:09:37.972228:   - Antenna: 1
2025-08-26 16:09:37.972228:   - TagFrequency: 0
2025-08-26 16:09:38.455658: 🔄 开始RFID轮询检查...
2025-08-26 16:09:38.455658: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:38.455658: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:38.455658: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:38.456654: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:38.456654: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:38.456654: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:38.458647: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:38.459644:   - 设备句柄: 1673073161888
2025-08-26 16:09:38.459644:   - FetchRecords返回值: 0
2025-08-26 16:09:38.459644:   - 报告数量: 5
2025-08-26 16:09:38.459644:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.459644:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:38.459644:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.459644:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.460641:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.460641:   - 数据长度: 8
2025-08-26 16:09:38.460641:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:38.460641:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:38.460641:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.460641:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:38.460641:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.460641:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.461637:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.461637:   - 数据长度: 8
2025-08-26 16:09:38.461637:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:38.461637:   - 提取的UID: E004015305F68508
2025-08-26 16:09:38.461637:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.461637:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:38.461637:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.462634:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.462634:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.462634:   - 数据长度: 8
2025-08-26 16:09:38.462634:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:38.462634:   - 提取的UID: E004015305F68508
2025-08-26 16:09:38.462634:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.462634:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:38.462634:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.463631:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.463631:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.463631:   - 数据长度: 8
2025-08-26 16:09:38.463631:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:38.463631:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:38.463631:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.463631:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:38.463631:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.464627:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.464627:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.464627:   - 数据长度: 8
2025-08-26 16:09:38.464627:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:38.464627:   - 提取的UID: E004015305F68508
2025-08-26 16:09:38.464627: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:38.464627:   - 发现标签数量: 5
2025-08-26 16:09:38.464627:   - 标签详情:
2025-08-26 16:09:38.465624:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:38.465624:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:38.465624:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:38.465624:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:38.465624:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:38.465624: RFID扫描: 发现 5 个标签
2025-08-26 16:09:38.465624: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:38.465624: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:38.466621: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.466621:   - UID: E004015304F3DD22
2025-08-26 16:09:38.466621:   - Data: E004015304F3DD22
2025-08-26 16:09:38.466621:   - EventType: 1
2025-08-26 16:09:38.466621:   - Direction: 0
2025-08-26 16:09:38.466621:   - Antenna: 1
2025-08-26 16:09:38.466621:   - TagFrequency: 0
2025-08-26 16:09:38.466621: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.467617:   - UID: E004015305F68508
2025-08-26 16:09:38.467617:   - Data: E004015305F68508
2025-08-26 16:09:38.467617:   - EventType: 1
2025-08-26 16:09:38.467617:   - Direction: 0
2025-08-26 16:09:38.467617:   - Antenna: 1
2025-08-26 16:09:38.467617:   - TagFrequency: 0
2025-08-26 16:09:38.467617: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.467617:   - UID: E004015305F68508
2025-08-26 16:09:38.468614:   - Data: E004015305F68508
2025-08-26 16:09:38.468614:   - EventType: 1
2025-08-26 16:09:38.468614:   - Direction: 0
2025-08-26 16:09:38.468614:   - Antenna: 1
2025-08-26 16:09:38.468614:   - TagFrequency: 0
2025-08-26 16:09:38.468614: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.468614:   - UID: E004015304F3DD22
2025-08-26 16:09:38.469611:   - Data: E004015304F3DD22
2025-08-26 16:09:38.469611:   - EventType: 1
2025-08-26 16:09:38.469611:   - Direction: 0
2025-08-26 16:09:38.469611:   - Antenna: 1
2025-08-26 16:09:38.469611:   - TagFrequency: 0
2025-08-26 16:09:38.469611: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.469611:   - UID: E004015305F68508
2025-08-26 16:09:38.469611:   - Data: E004015305F68508
2025-08-26 16:09:38.470608:   - EventType: 1
2025-08-26 16:09:38.470608:   - Direction: 0
2025-08-26 16:09:38.470608:   - Antenna: 1
2025-08-26 16:09:38.471607:   - TagFrequency: 0
2025-08-26 16:09:38.956996: 🔄 开始RFID轮询检查...
2025-08-26 16:09:38.956996: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:38.956996: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:38.957992: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:38.957992: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:38.957992: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:38.957992: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:38.959985: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:38.960982:   - 设备句柄: 1673073161888
2025-08-26 16:09:38.960982:   - FetchRecords返回值: 0
2025-08-26 16:09:38.960982:   - 报告数量: 5
2025-08-26 16:09:38.960982:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.960982:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:38.960982:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.960982:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.961979:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.961979:   - 数据长度: 8
2025-08-26 16:09:38.961979:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:38.961979:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:38.961979:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.961979:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:38.961979:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.961979:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.962975:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.962975:   - 数据长度: 8
2025-08-26 16:09:38.962975:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:38.962975:   - 提取的UID: E004015305F68508
2025-08-26 16:09:38.962975:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.963972:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:38.963972:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.963972:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.963972:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.964970:   - 数据长度: 8
2025-08-26 16:09:38.964970:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:38.964970:   - 提取的UID: E004015305F68508
2025-08-26 16:09:38.964970:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.964970:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:38.965967:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.965967:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.965967:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.965967:   - 数据长度: 8
2025-08-26 16:09:38.965967:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:38.966962:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:38.966962:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:38.966962:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:38.966962:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:38.966962:   - 设备类型: LSGControlCenter
2025-08-26 16:09:38.966962:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:38.966962:   - 数据长度: 8
2025-08-26 16:09:38.967959:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:38.967959:   - 提取的UID: E004015305F68508
2025-08-26 16:09:38.967959: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:38.967959:   - 发现标签数量: 5
2025-08-26 16:09:38.967959:   - 标签详情:
2025-08-26 16:09:38.967959:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:38.967959:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:38.967959:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:38.968955:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:38.968955:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:38.968955: RFID扫描: 发现 5 个标签
2025-08-26 16:09:38.968955: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:38.968955: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:38.968955: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.968955:   - UID: E004015304F3DD22
2025-08-26 16:09:38.969952:   - Data: E004015304F3DD22
2025-08-26 16:09:38.969952:   - EventType: 1
2025-08-26 16:09:38.969952:   - Direction: 0
2025-08-26 16:09:38.969952:   - Antenna: 1
2025-08-26 16:09:38.969952:   - TagFrequency: 0
2025-08-26 16:09:38.969952: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.969952:   - UID: E004015305F68508
2025-08-26 16:09:38.969952:   - Data: E004015305F68508
2025-08-26 16:09:38.970949:   - EventType: 1
2025-08-26 16:09:38.970949:   - Direction: 0
2025-08-26 16:09:38.970949:   - Antenna: 1
2025-08-26 16:09:38.970949:   - TagFrequency: 0
2025-08-26 16:09:38.970949: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.970949:   - UID: E004015305F68508
2025-08-26 16:09:38.970949:   - Data: E004015305F68508
2025-08-26 16:09:38.970949:   - EventType: 1
2025-08-26 16:09:38.971945:   - Direction: 0
2025-08-26 16:09:38.971945:   - Antenna: 1
2025-08-26 16:09:38.971945:   - TagFrequency: 0
2025-08-26 16:09:38.971945: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.971945:   - UID: E004015304F3DD22
2025-08-26 16:09:38.971945:   - Data: E004015304F3DD22
2025-08-26 16:09:38.971945:   - EventType: 1
2025-08-26 16:09:38.971945:   - Direction: 0
2025-08-26 16:09:38.972942:   - Antenna: 1
2025-08-26 16:09:38.972942:   - TagFrequency: 0
2025-08-26 16:09:38.972942: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:38.972942:   - UID: E004015305F68508
2025-08-26 16:09:38.972942:   - Data: E004015305F68508
2025-08-26 16:09:38.972942:   - EventType: 1
2025-08-26 16:09:38.972942:   - Direction: 0
2025-08-26 16:09:38.972942:   - Antenna: 1
2025-08-26 16:09:38.973939:   - TagFrequency: 0
2025-08-26 16:09:39.455342: 🔄 开始RFID轮询检查...
2025-08-26 16:09:39.455342: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:39.455342: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:39.455342: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:39.456340: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:39.456340: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:39.456340: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:39.459329: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:39.459329:   - 设备句柄: 1673073161888
2025-08-26 16:09:39.460327:   - FetchRecords返回值: 0
2025-08-26 16:09:39.460327:   - 报告数量: 5
2025-08-26 16:09:39.460327:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.460327:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:39.460327:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.460327:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.461323:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.461323:   - 数据长度: 8
2025-08-26 16:09:39.461323:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:39.461323:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:39.461323:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.461323:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:39.461323:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.462319:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.462319:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.462319:   - 数据长度: 8
2025-08-26 16:09:39.462319:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:39.462319:   - 提取的UID: E004015305F68508
2025-08-26 16:09:39.462319:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.462319:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:39.462319:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.463316:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.463316:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.463316:   - 数据长度: 8
2025-08-26 16:09:39.463316:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:39.463316:   - 提取的UID: E004015305F68508
2025-08-26 16:09:39.463316:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.463316:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:39.463316:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.464313:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.464313:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.464313:   - 数据长度: 8
2025-08-26 16:09:39.464313:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:39.464313:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:39.464313:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.464313:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:39.464313:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.465309:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.465309:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.465309:   - 数据长度: 8
2025-08-26 16:09:39.465309:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:39.465309:   - 提取的UID: E004015305F68508
2025-08-26 16:09:39.465309: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:39.465309:   - 发现标签数量: 5
2025-08-26 16:09:39.466306:   - 标签详情:
2025-08-26 16:09:39.466306:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:39.466306:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:39.466306:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:39.466306:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:39.466306:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:39.466306: RFID扫描: 发现 5 个标签
2025-08-26 16:09:39.466306: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:39.467303: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:39.467303: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.467303:   - UID: E004015304F3DD22
2025-08-26 16:09:39.467303:   - Data: E004015304F3DD22
2025-08-26 16:09:39.467303:   - EventType: 1
2025-08-26 16:09:39.467303:   - Direction: 0
2025-08-26 16:09:39.467303:   - Antenna: 1
2025-08-26 16:09:39.467303:   - TagFrequency: 0
2025-08-26 16:09:39.468299: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.468299:   - UID: E004015305F68508
2025-08-26 16:09:39.468299:   - Data: E004015305F68508
2025-08-26 16:09:39.468299:   - EventType: 1
2025-08-26 16:09:39.468299:   - Direction: 0
2025-08-26 16:09:39.468299:   - Antenna: 1
2025-08-26 16:09:39.468299:   - TagFrequency: 0
2025-08-26 16:09:39.468299: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.469296:   - UID: E004015305F68508
2025-08-26 16:09:39.469296:   - Data: E004015305F68508
2025-08-26 16:09:39.469296:   - EventType: 1
2025-08-26 16:09:39.469296:   - Direction: 0
2025-08-26 16:09:39.469296:   - Antenna: 1
2025-08-26 16:09:39.469296:   - TagFrequency: 0
2025-08-26 16:09:39.469296: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.469296:   - UID: E004015304F3DD22
2025-08-26 16:09:39.470293:   - Data: E004015304F3DD22
2025-08-26 16:09:39.470293:   - EventType: 1
2025-08-26 16:09:39.470293:   - Direction: 0
2025-08-26 16:09:39.470293:   - Antenna: 1
2025-08-26 16:09:39.470293:   - TagFrequency: 0
2025-08-26 16:09:39.470293: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.470293:   - UID: E004015305F68508
2025-08-26 16:09:39.470293:   - Data: E004015305F68508
2025-08-26 16:09:39.471290:   - EventType: 1
2025-08-26 16:09:39.471290:   - Direction: 0
2025-08-26 16:09:39.471290:   - Antenna: 1
2025-08-26 16:09:39.471290:   - TagFrequency: 0
2025-08-26 16:09:39.956199: 🔄 开始RFID轮询检查...
2025-08-26 16:09:39.956199: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:39.956199: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:39.956199: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:39.957196: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:39.957196: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:39.957196: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:39.959189: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:39.960186:   - 设备句柄: 1673073161888
2025-08-26 16:09:39.960186:   - FetchRecords返回值: 0
2025-08-26 16:09:39.960186:   - 报告数量: 5
2025-08-26 16:09:39.960186:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.960186:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:39.960186:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.960186:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.960186:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.961182:   - 数据长度: 8
2025-08-26 16:09:39.961182:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:39.961182:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:39.961182:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.961182:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:39.961182:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.961182:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.962179:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.962179:   - 数据长度: 8
2025-08-26 16:09:39.962179:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:39.962179:   - 提取的UID: E004015305F68508
2025-08-26 16:09:39.962179:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.962179:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:39.962179:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.962179:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.963176:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.963176:   - 数据长度: 8
2025-08-26 16:09:39.963176:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:39.963176:   - 提取的UID: E004015305F68508
2025-08-26 16:09:39.963176:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.963176:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:39.963176:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.964174:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.964174:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.964174:   - 数据长度: 8
2025-08-26 16:09:39.964174:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:39.964174:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:39.964174:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:39.964174:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:39.964174:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:39.965169:   - 设备类型: LSGControlCenter
2025-08-26 16:09:39.965169:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:39.965169:   - 数据长度: 8
2025-08-26 16:09:39.965169:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:39.965169:   - 提取的UID: E004015305F68508
2025-08-26 16:09:39.965169: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:39.965169:   - 发现标签数量: 5
2025-08-26 16:09:39.965169:   - 标签详情:
2025-08-26 16:09:39.966166:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:39.966166:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:39.966166:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:39.966166:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:39.966166:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:39.966166: RFID扫描: 发现 5 个标签
2025-08-26 16:09:39.966166: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:39.966166: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:39.967163: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.967163:   - UID: E004015304F3DD22
2025-08-26 16:09:39.967163:   - Data: E004015304F3DD22
2025-08-26 16:09:39.967163:   - EventType: 1
2025-08-26 16:09:39.967163:   - Direction: 0
2025-08-26 16:09:39.967163:   - Antenna: 1
2025-08-26 16:09:39.967163:   - TagFrequency: 0
2025-08-26 16:09:39.967163: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.968159:   - UID: E004015305F68508
2025-08-26 16:09:39.968159:   - Data: E004015305F68508
2025-08-26 16:09:39.968159:   - EventType: 1
2025-08-26 16:09:39.968159:   - Direction: 0
2025-08-26 16:09:39.968159:   - Antenna: 1
2025-08-26 16:09:39.968159:   - TagFrequency: 0
2025-08-26 16:09:39.968159: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.968159:   - UID: E004015305F68508
2025-08-26 16:09:39.969156:   - Data: E004015305F68508
2025-08-26 16:09:39.969156:   - EventType: 1
2025-08-26 16:09:39.969156:   - Direction: 0
2025-08-26 16:09:39.969156:   - Antenna: 1
2025-08-26 16:09:39.969156:   - TagFrequency: 0
2025-08-26 16:09:39.969156: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.969156:   - UID: E004015304F3DD22
2025-08-26 16:09:39.969156:   - Data: E004015304F3DD22
2025-08-26 16:09:39.970153:   - EventType: 1
2025-08-26 16:09:39.970153:   - Direction: 0
2025-08-26 16:09:39.970153:   - Antenna: 1
2025-08-26 16:09:39.970153:   - TagFrequency: 0
2025-08-26 16:09:39.970153: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:39.970153:   - UID: E004015305F68508
2025-08-26 16:09:39.970153:   - Data: E004015305F68508
2025-08-26 16:09:39.970153:   - EventType: 1
2025-08-26 16:09:39.971149:   - Direction: 0
2025-08-26 16:09:39.971149:   - Antenna: 1
2025-08-26 16:09:39.971149:   - TagFrequency: 0
2025-08-26 16:09:40.455543: 🔄 开始RFID轮询检查...
2025-08-26 16:09:40.455543: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:40.455543: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:40.455543: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:40.456540: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:40.456540: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:40.456540: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:40.458533: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:40.459530:   - 设备句柄: 1673073161888
2025-08-26 16:09:40.459530:   - FetchRecords返回值: 0
2025-08-26 16:09:40.459530:   - 报告数量: 5
2025-08-26 16:09:40.459530:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.459530:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:40.459530:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.459530:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.460527:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.460527:   - 数据长度: 8
2025-08-26 16:09:40.460527:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:40.460527:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:40.460527:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.460527:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:40.460527:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.460527:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.461523:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.461523:   - 数据长度: 8
2025-08-26 16:09:40.461523:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:40.461523:   - 提取的UID: E004015305F68508
2025-08-26 16:09:40.461523:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.461523:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:40.461523:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.462520:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.462520:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.462520:   - 数据长度: 8
2025-08-26 16:09:40.462520:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:40.462520:   - 提取的UID: E004015305F68508
2025-08-26 16:09:40.462520:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.462520:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:40.462520:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.463517:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.463517:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.463517:   - 数据长度: 8
2025-08-26 16:09:40.463517:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:40.463517:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:40.463517:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.463517:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:40.463517:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.464514:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.464514:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.464514:   - 数据长度: 8
2025-08-26 16:09:40.464514:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:40.464514:   - 提取的UID: E004015305F68508
2025-08-26 16:09:40.464514: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:40.464514:   - 发现标签数量: 5
2025-08-26 16:09:40.464514:   - 标签详情:
2025-08-26 16:09:40.465510:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:40.465510:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:40.465510:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:40.465510:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:40.465510:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:40.465510: RFID扫描: 发现 5 个标签
2025-08-26 16:09:40.465510: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:40.465510: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:40.465510: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.466507:   - UID: E004015304F3DD22
2025-08-26 16:09:40.466507:   - Data: E004015304F3DD22
2025-08-26 16:09:40.466507:   - EventType: 1
2025-08-26 16:09:40.466507:   - Direction: 0
2025-08-26 16:09:40.466507:   - Antenna: 1
2025-08-26 16:09:40.466507:   - TagFrequency: 0
2025-08-26 16:09:40.466507: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.466507:   - UID: E004015305F68508
2025-08-26 16:09:40.467503:   - Data: E004015305F68508
2025-08-26 16:09:40.467503:   - EventType: 1
2025-08-26 16:09:40.467503:   - Direction: 0
2025-08-26 16:09:40.467503:   - Antenna: 1
2025-08-26 16:09:40.467503:   - TagFrequency: 0
2025-08-26 16:09:40.467503: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.467503:   - UID: E004015305F68508
2025-08-26 16:09:40.468500:   - Data: E004015305F68508
2025-08-26 16:09:40.468500:   - EventType: 1
2025-08-26 16:09:40.468500:   - Direction: 0
2025-08-26 16:09:40.468500:   - Antenna: 1
2025-08-26 16:09:40.468500:   - TagFrequency: 0
2025-08-26 16:09:40.468500: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.468500:   - UID: E004015304F3DD22
2025-08-26 16:09:40.468500:   - Data: E004015304F3DD22
2025-08-26 16:09:40.468500:   - EventType: 1
2025-08-26 16:09:40.469497:   - Direction: 0
2025-08-26 16:09:40.469497:   - Antenna: 1
2025-08-26 16:09:40.469497:   - TagFrequency: 0
2025-08-26 16:09:40.469497: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.469497:   - UID: E004015305F68508
2025-08-26 16:09:40.469497:   - Data: E004015305F68508
2025-08-26 16:09:40.469497:   - EventType: 1
2025-08-26 16:09:40.470494:   - Direction: 0
2025-08-26 16:09:40.470494:   - Antenna: 1
2025-08-26 16:09:40.470494:   - TagFrequency: 0
2025-08-26 16:09:40.955884: 🔄 开始RFID轮询检查...
2025-08-26 16:09:40.955884: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:40.955884: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:40.955884: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:40.956881: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:40.956881: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:40.956881: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:40.958874: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:40.958874:   - 设备句柄: 1673073161888
2025-08-26 16:09:40.959871:   - FetchRecords返回值: 0
2025-08-26 16:09:40.959871:   - 报告数量: 5
2025-08-26 16:09:40.959871:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.959871:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:40.959871:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.959871:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.960867:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.960867:   - 数据长度: 8
2025-08-26 16:09:40.960867:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:40.960867:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:40.960867:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.960867:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:40.960867:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.960867:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.961864:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.961864:   - 数据长度: 8
2025-08-26 16:09:40.961864:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:40.961864:   - 提取的UID: E004015305F68508
2025-08-26 16:09:40.961864:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.961864:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:40.961864:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.961864:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.962861:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.962861:   - 数据长度: 8
2025-08-26 16:09:40.962861:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:40.962861:   - 提取的UID: E004015305F68508
2025-08-26 16:09:40.962861:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.962861:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:40.962861:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.963857:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.963857:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.963857:   - 数据长度: 8
2025-08-26 16:09:40.963857:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:40.963857:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:40.963857:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:40.963857:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:40.963857:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:40.964854:   - 设备类型: LSGControlCenter
2025-08-26 16:09:40.964854:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:40.964854:   - 数据长度: 8
2025-08-26 16:09:40.964854:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:40.964854:   - 提取的UID: E004015305F68508
2025-08-26 16:09:40.964854: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:40.964854:   - 发现标签数量: 5
2025-08-26 16:09:40.964854:   - 标签详情:
2025-08-26 16:09:40.964854:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:40.965851:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:40.965851:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:40.965851:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:40.965851:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:40.965851: RFID扫描: 发现 5 个标签
2025-08-26 16:09:40.965851: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:40.965851: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:40.965851: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.966847:   - UID: E004015304F3DD22
2025-08-26 16:09:40.966847:   - Data: E004015304F3DD22
2025-08-26 16:09:40.966847:   - EventType: 1
2025-08-26 16:09:40.966847:   - Direction: 0
2025-08-26 16:09:40.966847:   - Antenna: 1
2025-08-26 16:09:40.966847:   - TagFrequency: 0
2025-08-26 16:09:40.966847: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.966847:   - UID: E004015305F68508
2025-08-26 16:09:40.967844:   - Data: E004015305F68508
2025-08-26 16:09:40.967844:   - EventType: 1
2025-08-26 16:09:40.967844:   - Direction: 0
2025-08-26 16:09:40.967844:   - Antenna: 1
2025-08-26 16:09:40.967844:   - TagFrequency: 0
2025-08-26 16:09:40.967844: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.967844:   - UID: E004015305F68508
2025-08-26 16:09:40.967844:   - Data: E004015305F68508
2025-08-26 16:09:40.968841:   - EventType: 1
2025-08-26 16:09:40.968841:   - Direction: 0
2025-08-26 16:09:40.968841:   - Antenna: 1
2025-08-26 16:09:40.968841:   - TagFrequency: 0
2025-08-26 16:09:40.968841: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.968841:   - UID: E004015304F3DD22
2025-08-26 16:09:40.968841:   - Data: E004015304F3DD22
2025-08-26 16:09:40.968841:   - EventType: 1
2025-08-26 16:09:40.969838:   - Direction: 0
2025-08-26 16:09:40.969838:   - Antenna: 1
2025-08-26 16:09:40.969838:   - TagFrequency: 0
2025-08-26 16:09:40.969838: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:40.969838:   - UID: E004015305F68508
2025-08-26 16:09:40.969838:   - Data: E004015305F68508
2025-08-26 16:09:40.969838:   - EventType: 1
2025-08-26 16:09:40.969838:   - Direction: 0
2025-08-26 16:09:40.970834:   - Antenna: 1
2025-08-26 16:09:40.970834:   - TagFrequency: 0
2025-08-26 16:09:41.456777: 🔄 开始RFID轮询检查...
2025-08-26 16:09:41.456777: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:41.456777: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:41.456777: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:41.457774: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:41.457774: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:41.457774: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:41.459767: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:41.460764:   - 设备句柄: 1673073161888
2025-08-26 16:09:41.460764:   - FetchRecords返回值: 0
2025-08-26 16:09:41.460764:   - 报告数量: 5
2025-08-26 16:09:41.460764:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.460764:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:41.460764:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.460764:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.461761:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.461761:   - 数据长度: 8
2025-08-26 16:09:41.461761:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:41.461761:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:41.461761:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.461761:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:41.461761:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.461761:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.462757:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.462757:   - 数据长度: 8
2025-08-26 16:09:41.462757:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:41.462757:   - 提取的UID: E004015305F68508
2025-08-26 16:09:41.462757:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.462757:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:41.462757:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.462757:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.463754:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.463754:   - 数据长度: 8
2025-08-26 16:09:41.463754:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:41.463754:   - 提取的UID: E004015305F68508
2025-08-26 16:09:41.463754:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.463754:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:41.463754:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.464750:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.464750:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.464750:   - 数据长度: 8
2025-08-26 16:09:41.464750:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:41.464750:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:41.464750:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.464750:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:41.464750:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.465747:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.465747:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.465747:   - 数据长度: 8
2025-08-26 16:09:41.465747:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:41.465747:   - 提取的UID: E004015305F68508
2025-08-26 16:09:41.465747: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:41.465747:   - 发现标签数量: 5
2025-08-26 16:09:41.465747:   - 标签详情:
2025-08-26 16:09:41.466744:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:41.466744:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:41.466744:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:41.466744:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:41.466744:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:41.466744: RFID扫描: 发现 5 个标签
2025-08-26 16:09:41.466744: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:41.466744: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:41.467741: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.467741:   - UID: E004015304F3DD22
2025-08-26 16:09:41.467741:   - Data: E004015304F3DD22
2025-08-26 16:09:41.467741:   - EventType: 1
2025-08-26 16:09:41.467741:   - Direction: 0
2025-08-26 16:09:41.467741:   - Antenna: 1
2025-08-26 16:09:41.467741:   - TagFrequency: 0
2025-08-26 16:09:41.467741: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.467741:   - UID: E004015305F68508
2025-08-26 16:09:41.468737:   - Data: E004015305F68508
2025-08-26 16:09:41.468737:   - EventType: 1
2025-08-26 16:09:41.468737:   - Direction: 0
2025-08-26 16:09:41.468737:   - Antenna: 1
2025-08-26 16:09:41.468737:   - TagFrequency: 0
2025-08-26 16:09:41.468737: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.468737:   - UID: E004015305F68508
2025-08-26 16:09:41.469734:   - Data: E004015305F68508
2025-08-26 16:09:41.469734:   - EventType: 1
2025-08-26 16:09:41.469734:   - Direction: 0
2025-08-26 16:09:41.469734:   - Antenna: 1
2025-08-26 16:09:41.469734:   - TagFrequency: 0
2025-08-26 16:09:41.469734: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.469734:   - UID: E004015304F3DD22
2025-08-26 16:09:41.469734:   - Data: E004015304F3DD22
2025-08-26 16:09:41.469734:   - EventType: 1
2025-08-26 16:09:41.470730:   - Direction: 0
2025-08-26 16:09:41.470730:   - Antenna: 1
2025-08-26 16:09:41.470730:   - TagFrequency: 0
2025-08-26 16:09:41.470730: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.470730:   - UID: E004015305F68508
2025-08-26 16:09:41.470730:   - Data: E004015305F68508
2025-08-26 16:09:41.470730:   - EventType: 1
2025-08-26 16:09:41.471727:   - Direction: 0
2025-08-26 16:09:41.471727:   - Antenna: 1
2025-08-26 16:09:41.471727:   - TagFrequency: 0
2025-08-26 16:09:41.956121: 🔄 开始RFID轮询检查...
2025-08-26 16:09:41.956121: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:41.956121: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:41.956121: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:41.957118: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:41.957118: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:41.957118: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:41.959111: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:41.960108:   - 设备句柄: 1673073161888
2025-08-26 16:09:41.960108:   - FetchRecords返回值: 0
2025-08-26 16:09:41.960108:   - 报告数量: 5
2025-08-26 16:09:41.960108:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.960108:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:41.960108:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.960108:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.961105:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.961105:   - 数据长度: 8
2025-08-26 16:09:41.961105:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:41.961105:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:41.961105:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.961105:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:41.961105:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.962101:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.962101:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.962101:   - 数据长度: 8
2025-08-26 16:09:41.962101:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:41.962101:   - 提取的UID: E004015305F68508
2025-08-26 16:09:41.962101:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.962101:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:41.962101:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.963098:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.963098:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.963098:   - 数据长度: 8
2025-08-26 16:09:41.963098:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:41.963098:   - 提取的UID: E004015305F68508
2025-08-26 16:09:41.963098:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.963098:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:41.963098:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.964094:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.964094:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.964094:   - 数据长度: 8
2025-08-26 16:09:41.964094:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:41.964094:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:41.964094:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:41.964094:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:41.965091:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:41.965091:   - 设备类型: LSGControlCenter
2025-08-26 16:09:41.965091:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:41.965091:   - 数据长度: 8
2025-08-26 16:09:41.965091:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:41.965091:   - 提取的UID: E004015305F68508
2025-08-26 16:09:41.965091: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:41.965091:   - 发现标签数量: 5
2025-08-26 16:09:41.966088:   - 标签详情:
2025-08-26 16:09:41.966088:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:41.966088:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:41.966088:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:41.966088:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:41.966088:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:41.966088: RFID扫描: 发现 5 个标签
2025-08-26 16:09:41.966088: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:41.967085: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:41.967085: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.967085:   - UID: E004015304F3DD22
2025-08-26 16:09:41.967085:   - Data: E004015304F3DD22
2025-08-26 16:09:41.967085:   - EventType: 1
2025-08-26 16:09:41.967085:   - Direction: 0
2025-08-26 16:09:41.967085:   - Antenna: 1
2025-08-26 16:09:41.967085:   - TagFrequency: 0
2025-08-26 16:09:41.968081: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.968081:   - UID: E004015305F68508
2025-08-26 16:09:41.968081:   - Data: E004015305F68508
2025-08-26 16:09:41.968081:   - EventType: 1
2025-08-26 16:09:41.968081:   - Direction: 0
2025-08-26 16:09:41.968081:   - Antenna: 1
2025-08-26 16:09:41.968081:   - TagFrequency: 0
2025-08-26 16:09:41.968081: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.969078:   - UID: E004015305F68508
2025-08-26 16:09:41.969078:   - Data: E004015305F68508
2025-08-26 16:09:41.969078:   - EventType: 1
2025-08-26 16:09:41.969078:   - Direction: 0
2025-08-26 16:09:41.969078:   - Antenna: 1
2025-08-26 16:09:41.969078:   - TagFrequency: 0
2025-08-26 16:09:41.969078: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.969078:   - UID: E004015304F3DD22
2025-08-26 16:09:41.970074:   - Data: E004015304F3DD22
2025-08-26 16:09:41.970074:   - EventType: 1
2025-08-26 16:09:41.970074:   - Direction: 0
2025-08-26 16:09:41.970074:   - Antenna: 1
2025-08-26 16:09:41.970074:   - TagFrequency: 0
2025-08-26 16:09:41.970074: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:41.970074:   - UID: E004015305F68508
2025-08-26 16:09:41.970074:   - Data: E004015305F68508
2025-08-26 16:09:41.971071:   - EventType: 1
2025-08-26 16:09:41.971071:   - Direction: 0
2025-08-26 16:09:41.971071:   - Antenna: 1
2025-08-26 16:09:41.971071:   - TagFrequency: 0
2025-08-26 16:09:42.455465: 🔄 开始RFID轮询检查...
2025-08-26 16:09:42.455465: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:42.455465: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:42.455465: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:42.455465: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:42.456462: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:42.456462: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:42.458455: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:42.459452:   - 设备句柄: 1673073161888
2025-08-26 16:09:42.459452:   - FetchRecords返回值: 0
2025-08-26 16:09:42.459452:   - 报告数量: 5
2025-08-26 16:09:42.459452:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.459452:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:42.459452:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.459452:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.460449:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.460449:   - 数据长度: 8
2025-08-26 16:09:42.460449:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:42.460449:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:42.460449:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.460449:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:42.460449:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.460449:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.461445:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.461445:   - 数据长度: 8
2025-08-26 16:09:42.461445:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:42.461445:   - 提取的UID: E004015305F68508
2025-08-26 16:09:42.461445:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.461445:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:42.461445:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.462442:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.462442:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.462442:   - 数据长度: 8
2025-08-26 16:09:42.462442:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:42.462442:   - 提取的UID: E004015305F68508
2025-08-26 16:09:42.462442:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.462442:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:42.462442:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.463439:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.463439:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.463439:   - 数据长度: 8
2025-08-26 16:09:42.463439:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:42.463439:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:42.463439:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.463439:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:42.463439:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.464435:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.464435:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.464435:   - 数据长度: 8
2025-08-26 16:09:42.464435:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:42.464435:   - 提取的UID: E004015305F68508
2025-08-26 16:09:42.464435: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:42.464435:   - 发现标签数量: 5
2025-08-26 16:09:42.465432:   - 标签详情:
2025-08-26 16:09:42.465432:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:42.465432:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:42.465432:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:42.465432:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:42.465432:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:42.465432: RFID扫描: 发现 5 个标签
2025-08-26 16:09:42.465432: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:42.466429: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:42.466429: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.466429:   - UID: E004015304F3DD22
2025-08-26 16:09:42.466429:   - Data: E004015304F3DD22
2025-08-26 16:09:42.466429:   - EventType: 1
2025-08-26 16:09:42.466429:   - Direction: 0
2025-08-26 16:09:42.466429:   - Antenna: 1
2025-08-26 16:09:42.466429:   - TagFrequency: 0
2025-08-26 16:09:42.467426: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.467426:   - UID: E004015305F68508
2025-08-26 16:09:42.467426:   - Data: E004015305F68508
2025-08-26 16:09:42.467426:   - EventType: 1
2025-08-26 16:09:42.467426:   - Direction: 0
2025-08-26 16:09:42.467426:   - Antenna: 1
2025-08-26 16:09:42.467426:   - TagFrequency: 0
2025-08-26 16:09:42.467426: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.468422:   - UID: E004015305F68508
2025-08-26 16:09:42.468422:   - Data: E004015305F68508
2025-08-26 16:09:42.468422:   - EventType: 1
2025-08-26 16:09:42.468422:   - Direction: 0
2025-08-26 16:09:42.468422:   - Antenna: 1
2025-08-26 16:09:42.468422:   - TagFrequency: 0
2025-08-26 16:09:42.468422: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.468422:   - UID: E004015304F3DD22
2025-08-26 16:09:42.469419:   - Data: E004015304F3DD22
2025-08-26 16:09:42.469419:   - EventType: 1
2025-08-26 16:09:42.469419:   - Direction: 0
2025-08-26 16:09:42.469419:   - Antenna: 1
2025-08-26 16:09:42.469419:   - TagFrequency: 0
2025-08-26 16:09:42.470417: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.470417:   - UID: E004015305F68508
2025-08-26 16:09:42.470417:   - Data: E004015305F68508
2025-08-26 16:09:42.470417:   - EventType: 1
2025-08-26 16:09:42.470417:   - Direction: 0
2025-08-26 16:09:42.470417:   - Antenna: 1
2025-08-26 16:09:42.470417:   - TagFrequency: 0
2025-08-26 16:09:42.955806: 🔄 开始RFID轮询检查...
2025-08-26 16:09:42.955806: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:42.955806: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:42.955806: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:42.956811: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:42.956811: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:42.956811: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:42.958796: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:42.959793:   - 设备句柄: 1673073161888
2025-08-26 16:09:42.959793:   - FetchRecords返回值: 0
2025-08-26 16:09:42.959793:   - 报告数量: 5
2025-08-26 16:09:42.959793:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.959793:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:42.959793:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.960790:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.960790:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.960790:   - 数据长度: 8
2025-08-26 16:09:42.960790:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:42.960790:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:42.960790:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.960790:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:42.960790:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.961786:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.961786:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.961786:   - 数据长度: 8
2025-08-26 16:09:42.961786:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:42.961786:   - 提取的UID: E004015305F68508
2025-08-26 16:09:42.961786:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.961786:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:42.961786:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.962783:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.962783:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.962783:   - 数据长度: 8
2025-08-26 16:09:42.962783:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:42.962783:   - 提取的UID: E004015305F68508
2025-08-26 16:09:42.962783:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.962783:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:42.963780:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.963780:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.963780:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.963780:   - 数据长度: 8
2025-08-26 16:09:42.963780:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:42.963780:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:42.963780:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:42.963780:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:42.964776:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:42.964776:   - 设备类型: LSGControlCenter
2025-08-26 16:09:42.964776:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:42.964776:   - 数据长度: 8
2025-08-26 16:09:42.964776:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:42.964776:   - 提取的UID: E004015305F68508
2025-08-26 16:09:42.964776: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:42.964776:   - 发现标签数量: 5
2025-08-26 16:09:42.965773:   - 标签详情:
2025-08-26 16:09:42.965773:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:42.965773:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:42.965773:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:42.965773:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:42.965773:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:42.965773: RFID扫描: 发现 5 个标签
2025-08-26 16:09:42.966770: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:42.966770: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:42.966770: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.966770:   - UID: E004015304F3DD22
2025-08-26 16:09:42.966770:   - Data: E004015304F3DD22
2025-08-26 16:09:42.966770:   - EventType: 1
2025-08-26 16:09:42.966770:   - Direction: 0
2025-08-26 16:09:42.966770:   - Antenna: 1
2025-08-26 16:09:42.967767:   - TagFrequency: 0
2025-08-26 16:09:42.967767: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.967767:   - UID: E004015305F68508
2025-08-26 16:09:42.967767:   - Data: E004015305F68508
2025-08-26 16:09:42.967767:   - EventType: 1
2025-08-26 16:09:42.967767:   - Direction: 0
2025-08-26 16:09:42.967767:   - Antenna: 1
2025-08-26 16:09:42.967767:   - TagFrequency: 0
2025-08-26 16:09:42.968763: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.968763:   - UID: E004015305F68508
2025-08-26 16:09:42.968763:   - Data: E004015305F68508
2025-08-26 16:09:42.968763:   - EventType: 1
2025-08-26 16:09:42.968763:   - Direction: 0
2025-08-26 16:09:42.968763:   - Antenna: 1
2025-08-26 16:09:42.968763:   - TagFrequency: 0
2025-08-26 16:09:42.968763: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.968763:   - UID: E004015304F3DD22
2025-08-26 16:09:42.969760:   - Data: E004015304F3DD22
2025-08-26 16:09:42.969760:   - EventType: 1
2025-08-26 16:09:42.969760:   - Direction: 0
2025-08-26 16:09:42.969760:   - Antenna: 1
2025-08-26 16:09:42.969760:   - TagFrequency: 0
2025-08-26 16:09:42.969760: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:42.969760:   - UID: E004015305F68508
2025-08-26 16:09:42.969760:   - Data: E004015305F68508
2025-08-26 16:09:42.970756:   - EventType: 1
2025-08-26 16:09:42.970756:   - Direction: 0
2025-08-26 16:09:42.970756:   - Antenna: 1
2025-08-26 16:09:42.970756:   - TagFrequency: 0
2025-08-26 16:09:43.017601: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:43.017601: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:43.018598: 🔍 数据长度: 8 字节
2025-08-26 16:09:43.018598: 🔍 预定义命令列表:
2025-08-26 16:09:43.018598:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 16:09:43.018598:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 16:09:43.018598:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 16:09:43.018598:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 16:09:43.018598:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 16:09:43.018598:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 16:09:43.018598:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 16:09:43.019595:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 16:09:43.019595:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 16:09:43.019595:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 16:09:43.019595: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 16:09:43.019595: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 16:09:43.019595: 收到闸机命令: exit_end (出馆结束)
2025-08-26 16:09:43.019595: 出馆流程结束
2025-08-26 16:09:43.019595: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 16:09:43.020591: [channel_1] 收到闸机事件: exit_end
2025-08-26 16:09:43.020591: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 16:09:43.020591: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 16:09:43.020591: [channel_1] 处理队列已清空
2025-08-26 16:09:43.020591: 📨 收到GateCoordinator事件: exit_end
2025-08-26 16:09:43.020591: 页面状态变更: SilencePageState.welcome
2025-08-26 16:09:43.020591: [channel_1] 通知收集到的条码: []
2025-08-26 16:09:43.020591: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 16:09:43.456147: 🔄 开始RFID轮询检查...
2025-08-26 16:09:43.456147: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:43.457146: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:43.457146: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:43.457146: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:43.457146: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:43.457146: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:43.459137: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:43.460133:   - 设备句柄: 1673073161888
2025-08-26 16:09:43.460133:   - FetchRecords返回值: 0
2025-08-26 16:09:43.460133:   - 报告数量: 5
2025-08-26 16:09:43.460133:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.460133:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:43.460133:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.461131:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.461131:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.461131:   - 数据长度: 8
2025-08-26 16:09:43.461131:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:43.461131:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:43.461131:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.461131:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:43.461131:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.462127:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.462127:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.462127:   - 数据长度: 8
2025-08-26 16:09:43.462127:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:43.462127:   - 提取的UID: E004015305F68508
2025-08-26 16:09:43.462127:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.462127:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:43.462127:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.463126:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.463126:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.463126:   - 数据长度: 8
2025-08-26 16:09:43.463126:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:43.463126:   - 提取的UID: E004015305F68508
2025-08-26 16:09:43.463126:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.463126:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:43.464120:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.464120:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.464120:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.464120:   - 数据长度: 8
2025-08-26 16:09:43.464120:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:43.464120:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:43.464120:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.464120:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:43.465117:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.465117:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.465117:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.465117:   - 数据长度: 8
2025-08-26 16:09:43.465117:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:43.465117:   - 提取的UID: E004015305F68508
2025-08-26 16:09:43.465117: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:43.466114:   - 发现标签数量: 5
2025-08-26 16:09:43.466114:   - 标签详情:
2025-08-26 16:09:43.466114:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:43.466114:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:43.466114:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:43.466114:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:43.466114:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:43.466114: RFID扫描: 发现 5 个标签
2025-08-26 16:09:43.466114: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:43.467110: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:43.467110: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.467110:   - UID: E004015304F3DD22
2025-08-26 16:09:43.467110:   - Data: E004015304F3DD22
2025-08-26 16:09:43.467110:   - EventType: 1
2025-08-26 16:09:43.467110:   - Direction: 0
2025-08-26 16:09:43.467110:   - Antenna: 1
2025-08-26 16:09:43.467110:   - TagFrequency: 0
2025-08-26 16:09:43.468107: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.468107:   - UID: E004015305F68508
2025-08-26 16:09:43.468107:   - Data: E004015305F68508
2025-08-26 16:09:43.468107:   - EventType: 1
2025-08-26 16:09:43.468107:   - Direction: 0
2025-08-26 16:09:43.468107:   - Antenna: 1
2025-08-26 16:09:43.468107:   - TagFrequency: 0
2025-08-26 16:09:43.468107: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.469104:   - UID: E004015305F68508
2025-08-26 16:09:43.469104:   - Data: E004015305F68508
2025-08-26 16:09:43.469104:   - EventType: 1
2025-08-26 16:09:43.469104:   - Direction: 0
2025-08-26 16:09:43.469104:   - Antenna: 1
2025-08-26 16:09:43.469104:   - TagFrequency: 0
2025-08-26 16:09:43.469104: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.469104:   - UID: E004015304F3DD22
2025-08-26 16:09:43.470100:   - Data: E004015304F3DD22
2025-08-26 16:09:43.470100:   - EventType: 1
2025-08-26 16:09:43.470100:   - Direction: 0
2025-08-26 16:09:43.470100:   - Antenna: 1
2025-08-26 16:09:43.470100:   - TagFrequency: 0
2025-08-26 16:09:43.470100: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.470100:   - UID: E004015305F68508
2025-08-26 16:09:43.470100:   - Data: E004015305F68508
2025-08-26 16:09:43.471097:   - EventType: 1
2025-08-26 16:09:43.471097:   - Direction: 0
2025-08-26 16:09:43.471097:   - Antenna: 1
2025-08-26 16:09:43.471097:   - TagFrequency: 0
2025-08-26 16:09:43.956488: 🔄 开始RFID轮询检查...
2025-08-26 16:09:43.956488: 📊 轮询状态: 扫描中=true, tagList=2个标签, 已处理=2个
2025-08-26 16:09:43.956488: 🏷️ 检查标签: barcode=null, uid=E004015304F3DD22
2025-08-26 16:09:43.956488: 🔄 标签已处理(UID): E004015304F3DD22
2025-08-26 16:09:43.956488: 🏷️ 检查标签: barcode=null, uid=E004015305F68508
2025-08-26 16:09:43.957485: 🔄 标签已处理(UID): E004015305F68508
2025-08-26 16:09:43.957485: 🔄 轮询完成: 无新标签，已处理2个标签
2025-08-26 16:09:43.959478: 🔍 LSGate硬件扫描详情:
2025-08-26 16:09:43.960475:   - 设备句柄: 1673073161888
2025-08-26 16:09:43.960475:   - FetchRecords返回值: 0
2025-08-26 16:09:43.960475:   - 报告数量: 5
2025-08-26 16:09:43.960475:   - 报告1 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.960475:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:43.960475:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.960475:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.961471:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.961471:   - 数据长度: 8
2025-08-26 16:09:43.961471:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:43.961471:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:43.961471:   - 报告2 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.961471:   - 原始数据: 01 00 19 08 1A 10 0A 23 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:43.961471:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.961471:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.962468:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.962468:   - 数据长度: 8
2025-08-26 16:09:43.962468:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:43.962468:   - 提取的UID: E004015305F68508
2025-08-26 16:09:43.962468:   - 报告3 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.962468:   - 原始数据: 01 00 19 08 1A 10 0A 24 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:43.962468:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.962468:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.963464:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.963464:   - 数据长度: 8
2025-08-26 16:09:43.963464:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:43.963464:   - 提取的UID: E004015305F68508
2025-08-26 16:09:43.963464:   - 报告4 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.963464:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 04 F3 DD 22 00 10 C1 02 07 0F 3D B9 D7 4E 38 2A 00 00 02
2025-08-26 16:09:43.963464:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.963464:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.964461:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.964461:   - 数据长度: 8
2025-08-26 16:09:43.964461:   - 标签数据: E0 04 01 53 04 F3 DD 22
2025-08-26 16:09:43.964461:   - 提取的UID: E004015304F3DD22
2025-08-26 16:09:43.964461:   - 报告5 解析结果: parseRet=0, 数据长度=35
2025-08-26 16:09:43.964461:   - 原始数据: 01 00 19 08 1A 10 0A 25 09 E0 04 01 53 05 F6 85 08 00 10 C1 02 07 0F 3D 32 CB 6D F3 CE 00 00 02
2025-08-26 16:09:43.964461:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 16:09:43.965458:   - 设备类型: LSGControlCenter
2025-08-26 16:09:43.965458:   - 事件类型: 1, 方向: 0
2025-08-26 16:09:43.965458:   - 数据长度: 8
2025-08-26 16:09:43.965458:   - 标签数据: E0 04 01 53 05 F6 85 08
2025-08-26 16:09:43.965458:   - 提取的UID: E004015305F68508
2025-08-26 16:09:43.965458: 📊 LSGate扫描结果汇总:
2025-08-26 16:09:43.965458:   - 发现标签数量: 5
2025-08-26 16:09:43.965458:   - 标签详情:
2025-08-26 16:09:43.966455:     [0] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:43.966455:     [1] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:43.966455:     [2] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:43.966455:     [3] UID: E004015304F3DD22, 事件: 1, 方向: 0
2025-08-26 16:09:43.966455:     [4] UID: E004015305F68508, 事件: 1, 方向: 0
2025-08-26 16:09:43.966455: RFID扫描: 发现 5 个标签
2025-08-26 16:09:43.966455: 🔍 LSGate ReaderManager收到扫描结果: 5 个UID
2025-08-26 16:09:43.966455: 🎉 LSGate检测到RFID标签！
2025-08-26 16:09:43.967452: 🏷️ LSGate UID[0]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.967452:   - UID: E004015304F3DD22
2025-08-26 16:09:43.967452:   - Data: E004015304F3DD22
2025-08-26 16:09:43.967452:   - EventType: 1
2025-08-26 16:09:43.967452:   - Direction: 0
2025-08-26 16:09:43.967452:   - Antenna: 1
2025-08-26 16:09:43.968453:   - TagFrequency: 0
2025-08-26 16:09:43.968453: 🏷️ LSGate UID[1]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 35], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.968453:   - UID: E004015305F68508
2025-08-26 16:09:43.968453:   - Data: E004015305F68508
2025-08-26 16:09:43.968453:   - EventType: 1
2025-08-26 16:09:43.968453:   - Direction: 0
2025-08-26 16:09:43.969445:   - Antenna: 1
2025-08-26 16:09:43.969445:   - TagFrequency: 0
2025-08-26 16:09:43.969445: 🏷️ LSGate UID[2]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 36], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.969445:   - UID: E004015305F68508
2025-08-26 16:09:43.970448:   - Data: E004015305F68508
2025-08-26 16:09:43.971440:   - EventType: 1
2025-08-26 16:09:43.971440:   - Direction: 0
2025-08-26 16:09:43.972438:   - Antenna: 1
2025-08-26 16:09:43.972438:   - TagFrequency: 0
2025-08-26 16:09:43.973432: 🏷️ LSGate UID[3]: {uid: E004015304F3DD22, data: E004015304F3DD22, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.973432:   - UID: E004015304F3DD22
2025-08-26 16:09:43.974432:   - Data: E004015304F3DD22
2025-08-26 16:09:43.974432:   - EventType: 1
2025-08-26 16:09:43.975433:   - Direction: 0
2025-08-26 16:09:43.975433:   - Antenna: 1
2025-08-26 16:09:43.975433:   - TagFrequency: 0
2025-08-26 16:09:43.976427: 🏷️ LSGate UID[4]: {uid: E004015305F68508, data: E004015305F68508, eventType: 1, direction: 0, time: [25, 8, 26, 16, 10, 37], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 16:09:43.976427:   - UID: E004015305F68508
2025-08-26 16:09:43.976427:   - Data: E004015305F68508
2025-08-26 16:09:43.976427:   - EventType: 1
2025-08-26 16:09:43.977419:   - Direction: 0
2025-08-26 16:09:43.977419:   - Antenna: 1
2025-08-26 16:09:43.977419:   - TagFrequency: 0
